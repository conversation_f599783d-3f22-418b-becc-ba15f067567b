<?php
/**
 * 微信消息处理测试脚本
 * 用于模拟微信服务器发送消息到我们的接口
 */

// 模拟微信图片消息XML
$imageMessageXml = '<xml>
<ToUserName><![CDATA[gh_123456789]]></ToUserName>
<FromUserName><![CDATA[openid_test_user_' . time() . ']]></FromUserName>
<CreateTime>' . time() . '</CreateTime>
<MsgType><![CDATA[image]]></MsgType>
<PicUrl><![CDATA[https://via.placeholder.com/300x200.jpg?text=Test+Image]]></PicUrl>
<MediaId><![CDATA[media_test_' . uniqid() . ']]></MediaId>
<MsgId>' . time() . rand(1000, 9999) . '</MsgId>
</xml>';

// 模拟微信文本消息XML（包含表情）
$textMessageXml = '<xml>
<ToUserName><![CDATA[gh_123456789]]></ToUserName>
<FromUserName><![CDATA[openid_test_user_' . time() . ']]></FromUserName>
<CreateTime>' . time() . '</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[你好 😊 这是一个测试消息 🎉]]></Content>
<MsgId>' . time() . rand(1000, 9999) . '</MsgId>
</xml>';

// 获取当前域名
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$baseUrl = $protocol . '://' . $host;

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>微信消息测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background: #fafafa; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #005a87; }
        .result { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 4px solid #007cba; }
        .error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }
        .success { border-left-color: #28a745; background: #d4edda; color: #155724; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap; }
        .xml-preview { background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 微信消息处理测试</h1>
        
        <div class='test-section'>
            <h3>📱 测试图片消息</h3>
            <p>模拟用户发送图片消息到微信公众号：</p>
            <div class='xml-preview'>" . htmlspecialchars($imageMessageXml) . "</div>
            <button class='btn' onclick='testImageMessage()'>发送图片消息</button>
            <div id='image-result'></div>
        </div>
        
        <div class='test-section'>
            <h3>💬 测试文本消息（包含表情）</h3>
            <p>模拟用户发送包含表情的文本消息：</p>
            <div class='xml-preview'>" . htmlspecialchars($textMessageXml) . "</div>
            <button class='btn' onclick='testTextMessage()'>发送文本消息</button>
            <div id='text-result'></div>
        </div>
        
        <div class='test-section'>
            <h3>🔍 调试工具</h3>
            <p>使用调试工具查看系统状态和日志：</p>
            <a href='/api/wechat/debug' class='btn' target='_blank'>打开调试页面</a>
            <a href='/images/manage' class='btn' target='_blank'>图片管理</a>
        </div>
        
        <div class='test-section'>
            <h3>📋 测试说明</h3>
            <ul>
                <li><strong>图片消息测试</strong>：会下载测试图片并保存到服务器</li>
                <li><strong>文本消息测试</strong>：会检测表情符号并记录</li>
                <li><strong>调试页面</strong>：可以查看详细的系统状态和日志</li>
                <li><strong>图片管理</strong>：可以查看所有保存的图片</li>
            </ul>
        </div>
    </div>
    
    <script>
        async function testImageMessage() {
            const resultDiv = document.getElementById('image-result');
            resultDiv.innerHTML = '<div class=\"result\">正在发送图片消息...</div>';
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: `" . addslashes($imageMessageXml) . "`
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <div class=\"result success\">
                        <h4>响应结果：</h4>
                        <pre>\${responseText}</pre>
                        <p><strong>状态码：</strong> \${response.status}</p>
                        <p><strong>提示：</strong> 如果返回XML格式的回复消息，说明处理成功！</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class=\"result error\">
                        <h4>请求失败：</h4>
                        <pre>\${error.message}</pre>
                    </div>
                `;
            }
        }
        
        async function testTextMessage() {
            const resultDiv = document.getElementById('text-result');
            resultDiv.innerHTML = '<div class=\"result\">正在发送文本消息...</div>';
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: `" . addslashes($textMessageXml) . "`
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <div class=\"result success\">
                        <h4>响应结果：</h4>
                        <pre>\${responseText}</pre>
                        <p><strong>状态码：</strong> \${response.status}</p>
                        <p><strong>提示：</strong> 如果返回包含表情检测信息的XML回复，说明处理成功！</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class=\"result error\">
                        <h4>请求失败：</h4>
                        <pre>\${error.message}</pre>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>";
?>
