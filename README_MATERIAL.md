# 微信公众号素材库接口

## 功能概述

这个接口实现了微信公众号素材库的批量获取功能，支持获取图片、语音、视频、缩略图和图文消息等各种类型的素材，并将详细数据保存到日志文件中。

## 接口信息

### 请求地址
```
GET /api/wechat/material/batch
```

### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| type | string | 否 | image | 素材类型：image(图片)、voice(语音)、video(视频)、thumb(缩略图)、news(图文消息) |
| offset | int | 否 | 0 | 偏移量，从第几个素材开始获取 |
| count | int | 否 | 20 | 获取数量，范围1-20 |

### 请求示例

```bash
# 获取图片素材
curl "http://yourdomain.com/api/wechat/material/batch?type=image&offset=0&count=10"

# 获取图文消息
curl "http://yourdomain.com/api/wechat/material/batch?type=news&offset=0&count=5"

# 获取语音素材
curl "http://yourdomain.com/api/wechat/material/batch?type=voice&offset=0&count=20"
```

### 响应格式

#### 成功响应
```json
{
    "code": 200,
    "message": "素材库获取成功",
    "data": {
        "total_count": 100,
        "item_count": 10,
        "item": [
            {
                "media_id": "media_id_123",
                "name": "image_name.jpg",
                "update_time": 1627296000,
                "url": "https://mmbiz.qpic.cn/..."
            }
        ]
    }
}
```

#### 错误响应
```json
{
    "code": 400,
    "message": "不支持的素材类型，支持的类型: image, voice, video, thumb, news",
    "data": null
}
```

## 配置要求

### 环境变量配置

在 `.env` 文件中配置以下参数：

```env
# 微信公众号AppID
WECHAT_APP_ID=your_app_id_here

# 微信公众号AppSecret  
WECHAT_APP_SECRET=your_app_secret_here

# 微信Token（用于消息验证）
WECHAT_TOKEN=your_token_here
```

### 微信公众平台配置

1. 登录微信公众平台
2. 进入"开发" -> "基本配置"
3. 获取AppID和AppSecret
4. 确保公众号有素材管理权限

## 日志记录

### 日志文件位置

1. **ThinkPHP系统日志**：`runtime/log/202507/26.log`
2. **素材专用日志**：`runtime/log/material_batch_get.log`

### 日志内容

#### 系统日志记录
- AccessToken获取过程
- API请求和响应
- 错误信息和异常

#### 素材专用日志记录
```json
{
    "timestamp": "2024-07-26 23:30:00",
    "data": {
        "type": "image",
        "total_count": 50,
        "item_count": 10,
        "materials": [
            {
                "index": 0,
                "media_id": "media_123",
                "name": "test.jpg",
                "url": "https://mmbiz.qpic.cn/...",
                "update_time": 1627296000,
                "update_time_formatted": "2024-07-26 23:30:00"
            }
        ]
    }
}
```

## 素材类型说明

### 1. 图片素材 (image)
- 支持JPG、PNG等格式
- 返回图片URL和基本信息
- 可直接用于消息发送

### 2. 语音素材 (voice)  
- 支持AMR、MP3格式
- 返回语音URL和时长信息
- 用于语音消息发送

### 3. 视频素材 (video)
- 支持MP4格式
- 返回视频下载URL和介绍
- 用于视频消息发送

### 4. 缩略图素材 (thumb)
- 用于视频消息的封面图
- 格式要求同图片素材

### 5. 图文消息 (news)
- 包含标题、作者、内容等
- 支持多图文消息
- 返回完整的图文信息

## 使用示例

### 测试页面
访问 `/test_material.html` 可以使用可视化界面测试接口。

### PHP代码示例
```php
// 获取图片素材
$response = file_get_contents('http://yourdomain.com/api/wechat/material/batch?type=image&count=10');
$data = json_decode($response, true);

if ($data['code'] === 200) {
    foreach ($data['data']['item'] as $item) {
        echo "图片: " . $item['name'] . " - " . $item['url'] . "\n";
    }
}
```

### JavaScript示例
```javascript
async function getMaterials() {
    try {
        const response = await fetch('/api/wechat/material/batch?type=image&count=5');
        const data = await response.json();
        
        if (data.code === 200) {
            console.log('总素材数:', data.data.total_count);
            console.log('本次获取:', data.data.item_count);
            data.data.item.forEach(item => {
                console.log('素材:', item.name, item.url);
            });
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}
```

## 错误处理

### 常见错误

1. **AppID或AppSecret未配置**
   ```json
   {"code": 400, "message": "获取AccessToken失败"}
   ```

2. **不支持的素材类型**
   ```json
   {"code": 400, "message": "不支持的素材类型，支持的类型: image, voice, video, thumb, news"}
   ```

3. **参数超出范围**
   ```json
   {"code": 400, "message": "count参数必须在1-20之间"}
   ```

4. **微信API错误**
   ```json
   {"code": 400, "message": "微信API错误: invalid appid (错误码: 40013)"}
   ```

### 调试方法

1. **查看日志**：检查 `runtime/log/` 目录下的日志文件
2. **使用调试工具**：访问 `/api/wechat/debug` 查看系统状态
3. **测试页面**：使用 `/test_material.html` 进行可视化测试

## 性能优化

### AccessToken缓存
- 自动缓存AccessToken，避免频繁请求
- 缓存时间为7200秒（2小时），提前5分钟刷新
- 使用ThinkPHP缓存系统

### 请求限制
- 单次最多获取20个素材
- 建议分页获取大量素材
- 避免频繁调用API

## 扩展功能

### 1. 素材下载
可以扩展接口支持素材文件的本地下载和保存。

### 2. 素材管理
可以添加素材的增删改查功能。

### 3. 定时同步
可以添加定时任务，定期同步素材库到本地数据库。

### 4. 素材分析
可以添加素材使用统计和分析功能。

## 安全注意事项

1. **保护AppSecret**：不要在前端代码中暴露AppSecret
2. **访问控制**：建议添加API访问权限控制
3. **频率限制**：避免过于频繁调用微信API
4. **日志安全**：注意日志文件的访问权限

## 故障排除

1. **检查配置**：确认AppID和AppSecret正确
2. **网络连接**：确保服务器能访问微信API
3. **权限检查**：确认公众号有相应的接口权限
4. **日志分析**：查看详细的错误日志信息
