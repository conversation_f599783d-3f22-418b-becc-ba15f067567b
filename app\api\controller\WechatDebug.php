<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\service\ImageService;

/**
 * 微信调试控制器
 */
class WechatDebug
{
    /**
     * 模拟微信图片消息
     */
    public function simulateImageMessage()
    {
        // 模拟微信图片消息XML
        $xmlData = '<xml>
<ToUserName><![CDATA[gh_123456789]]></ToUserName>
<FromUserName><![CDATA[openid_test_user]]></FromUserName>
<CreateTime>1627296000</CreateTime>
<MsgType><![CDATA[image]]></MsgType>
<PicUrl><![CDATA[https://mmbiz.qpic.cn/mmbiz_jpg/test.jpg]]></PicUrl>
<MediaId><![CDATA[media_test_123]]></MediaId>
<MsgId>1234567890</MsgId>
</xml>';

        // 解析消息
        $message = $this->parseMessage($xmlData);
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'xml_data' => $xmlData,
                'parsed_message' => $message,
                'test_result' => $message ? '解析成功' : '解析失败'
            ]
        ]);
    }
    
    /**
     * 测试图片下载
     */
    public function testImageDownload()
    {
        $testUrl = input('url', 'https://via.placeholder.com/300x200.jpg');
        $fromUser = 'test_user_' . time();
        
        $imageService = new ImageService();
        $result = $imageService->downloadWechatImage($testUrl, $fromUser, 'normal');
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'test_url' => $testUrl,
                'from_user' => $fromUser,
                'result' => $result
            ]
        ]);
    }
    
    /**
     * 查看最近的日志
     */
    public function viewLogs()
    {
        $logType = input('type', 'all'); // all, image, emoji
        $lines = (int)input('lines', 50);
        
        $logs = [];
        
        // 读取ThinkPHP日志
        $logPath = root_path() . 'runtime/log/' . date('Ym') . '/' . date('d') . '.log';
        if (file_exists($logPath)) {
            $content = file_get_contents($logPath);
            $logLines = explode("\n", $content);
            $logs['thinkphp'] = array_slice(array_reverse($logLines), 0, $lines);
        }
        
        // 读取图片保存日志
        if ($logType === 'all' || $logType === 'image') {
            $imageLogPath = root_path() . 'runtime/log/image_saves.log';
            if (file_exists($imageLogPath)) {
                $content = file_get_contents($imageLogPath);
                $logLines = explode("\n", trim($content));
                $logs['image_saves'] = array_slice(array_reverse($logLines), 0, $lines);
            }
        }
        
        // 读取表情使用日志
        if ($logType === 'all' || $logType === 'emoji') {
            $emojiLogPath = root_path() . 'runtime/log/emoji_usage.log';
            if (file_exists($emojiLogPath)) {
                $content = file_get_contents($emojiLogPath);
                $logLines = explode("\n", trim($content));
                $logs['emoji_usage'] = array_slice(array_reverse($logLines), 0, $lines);
            }
        }
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'log_type' => $logType,
                'lines' => $lines,
                'logs' => $logs
            ]
        ]);
    }
    
    /**
     * 检查系统状态
     */
    public function checkStatus()
    {
        $status = [
            'php_version' => PHP_VERSION,
            'extensions' => [
                'curl' => extension_loaded('curl'),
                'simplexml' => extension_loaded('simplexml'),
                'json' => extension_loaded('json'),
                'mbstring' => extension_loaded('mbstring'),
                'openssl' => extension_loaded('openssl')
            ],
            'directories' => [
                'upload_exists' => is_dir(root_path() . 'public/upload/'),
                'upload_writable' => is_writable(root_path() . 'public/upload/'),
                'log_exists' => is_dir(root_path() . 'runtime/log/'),
                'log_writable' => is_writable(root_path() . 'runtime/log/')
            ],
            'config' => [
                'wechat_token' => config('wechat.token', 'NOT_SET'),
                'max_execution_time' => ini_get('max_execution_time'),
                'memory_limit' => ini_get('memory_limit'),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size')
            ]
        ];
        
        // 检查上传目录权限
        $uploadDir = root_path() . 'public/upload/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $testFile = $uploadDir . 'test_' . time() . '.txt';
        $canWrite = file_put_contents($testFile, 'test') !== false;
        if ($canWrite && file_exists($testFile)) {
            unlink($testFile);
        }
        $status['directories']['upload_test_write'] = $canWrite;
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $status
        ]);
    }
    
    /**
     * 生成调试页面
     */
    public function debugPage()
    {
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #005a87; }
        .result { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 4px solid #007cba; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        input[type="text"] { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 微信调试工具</h1>
        
        <div class="section">
            <h3>系统状态检查</h3>
            <button class="btn" onclick="checkStatus()">检查系统状态</button>
            <div id="status-result"></div>
        </div>
        
        <div class="section">
            <h3>模拟微信消息</h3>
            <button class="btn" onclick="simulateMessage()">模拟图片消息</button>
            <div id="simulate-result"></div>
        </div>
        
        <div class="section">
            <h3>测试图片下载</h3>
            <input type="text" id="test-url" placeholder="输入图片URL" value="https://via.placeholder.com/300x200.jpg">
            <button class="btn" onclick="testDownload()">测试下载</button>
            <div id="download-result"></div>
        </div>
        
        <div class="section">
            <h3>查看日志</h3>
            <select id="log-type">
                <option value="all">全部日志</option>
                <option value="image">图片日志</option>
                <option value="emoji">表情日志</option>
            </select>
            <input type="number" id="log-lines" value="20" min="1" max="100" placeholder="行数">
            <button class="btn" onclick="viewLogs()">查看日志</button>
            <div id="logs-result"></div>
        </div>
    </div>
    
    <script>
        async function checkStatus() {
            try {
                const response = await fetch("/api/wechat/debug/status");
                const data = await response.json();
                document.getElementById("status-result").innerHTML = 
                    `<div class="result success"><pre>${JSON.stringify(data.data, null, 2)}</pre></div>`;
            } catch (error) {
                document.getElementById("status-result").innerHTML = 
                    `<div class="result error">错误: ${error.message}</div>`;
            }
        }
        
        async function simulateMessage() {
            try {
                const response = await fetch("/api/wechat/debug/simulate");
                const data = await response.json();
                document.getElementById("simulate-result").innerHTML = 
                    `<div class="result success"><pre>${JSON.stringify(data.data, null, 2)}</pre></div>`;
            } catch (error) {
                document.getElementById("simulate-result").innerHTML = 
                    `<div class="result error">错误: ${error.message}</div>`;
            }
        }
        
        async function testDownload() {
            const url = document.getElementById("test-url").value;
            try {
                const response = await fetch(`/api/wechat/debug/download?url=${encodeURIComponent(url)}`);
                const data = await response.json();
                document.getElementById("download-result").innerHTML = 
                    `<div class="result ${data.data.result.success ? "success" : "error"}">
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    </div>`;
            } catch (error) {
                document.getElementById("download-result").innerHTML = 
                    `<div class="result error">错误: ${error.message}</div>`;
            }
        }
        
        async function viewLogs() {
            const type = document.getElementById("log-type").value;
            const lines = document.getElementById("log-lines").value;
            try {
                const response = await fetch(`/api/wechat/debug/logs?type=${type}&lines=${lines}`);
                const data = await response.json();
                document.getElementById("logs-result").innerHTML = 
                    `<div class="result success"><pre>${JSON.stringify(data.data, null, 2)}</pre></div>`;
            } catch (error) {
                document.getElementById("logs-result").innerHTML = 
                    `<div class="result error">错误: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>';
        
        return response($html)->contentType('text/html');
    }
    
    /**
     * 解析微信消息XML（复制自Wechat控制器）
     */
    protected function parseMessage($xmlData)
    {
        if (empty($xmlData)) {
            return false;
        }
        
        try {
            $xml = simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml === false) {
                return false;
            }
            
            return [
                'ToUserName' => (string)$xml->ToUserName,
                'FromUserName' => (string)$xml->FromUserName,
                'CreateTime' => (string)$xml->CreateTime,
                'MsgType' => (string)$xml->MsgType,
                'MsgId' => (string)($xml->MsgId ?? ''),
                'Content' => (string)($xml->Content ?? ''),
                'MediaId' => (string)($xml->MediaId ?? ''),
                'PicUrl' => (string)($xml->PicUrl ?? ''),
                'Format' => (string)($xml->Format ?? ''),
                'Recognition' => (string)($xml->Recognition ?? ''),
                'ThumbMediaId' => (string)($xml->ThumbMediaId ?? ''),
                'Location_X' => (string)($xml->Location_X ?? ''),
                'Location_Y' => (string)($xml->Location_Y ?? ''),
                'Scale' => (string)($xml->Scale ?? ''),
                'Label' => (string)($xml->Label ?? ''),
                'Title' => (string)($xml->Title ?? ''),
                'Description' => (string)($xml->Description ?? ''),
                'Url' => (string)($xml->Url ?? ''),
                'Event' => (string)($xml->Event ?? ''),
                'EventKey' => (string)($xml->EventKey ?? ''),
            ];
        } catch (Exception $e) {
            return false;
        }
    }
}
