<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit24995f66c8d2004af38c3df167f92cdf
{
    public static $files = array (
        '9b552a3cc426e3287cc811caefa3cf53' => __DIR__ . '/..' . '/topthink/think-helper/src/helper.php',
        '35fab96057f1bf5e7aba31a8a6d5fdde' => __DIR__ . '/..' . '/topthink/think-orm/stubs/load_stubs.php',
    );

    public static $prefixLengthsPsr4 = array (
        't' => 
        array (
            'think\\' => 6,
        ),
        'a' => 
        array (
            'app\\' => 4,
        ),
        'P' => 
        array (
            'Psr\\SimpleCache\\' => 16,
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Container\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'think\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/framework/src/think',
            1 => __DIR__ . '/..' . '/topthink/think-orm/src',
            2 => __DIR__ . '/..' . '/topthink/think-helper/src',
        ),
        'app\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
        'Psr\\SimpleCache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/simple-cache/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/Psr/Log',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
    );

    public static $fallbackDirsPsr0 = array (
        0 => __DIR__ . '/../..' . '/extend',
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Psr\\Container\\ContainerExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerExceptionInterface.php',
        'Psr\\Container\\ContainerInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerInterface.php',
        'Psr\\Container\\NotFoundExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/NotFoundExceptionInterface.php',
        'Psr\\Http\\Message\\MessageInterface' => __DIR__ . '/..' . '/psr/http-message/src/MessageInterface.php',
        'Psr\\Http\\Message\\RequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/RequestInterface.php',
        'Psr\\Http\\Message\\ResponseInterface' => __DIR__ . '/..' . '/psr/http-message/src/ResponseInterface.php',
        'Psr\\Http\\Message\\ServerRequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/ServerRequestInterface.php',
        'Psr\\Http\\Message\\StreamInterface' => __DIR__ . '/..' . '/psr/http-message/src/StreamInterface.php',
        'Psr\\Http\\Message\\UploadedFileInterface' => __DIR__ . '/..' . '/psr/http-message/src/UploadedFileInterface.php',
        'Psr\\Http\\Message\\UriInterface' => __DIR__ . '/..' . '/psr/http-message/src/UriInterface.php',
        'Psr\\Log\\AbstractLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/AbstractLogger.php',
        'Psr\\Log\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/log/Psr/Log/InvalidArgumentException.php',
        'Psr\\Log\\LogLevel' => __DIR__ . '/..' . '/psr/log/Psr/Log/LogLevel.php',
        'Psr\\Log\\LoggerAwareInterface' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerAwareInterface.php',
        'Psr\\Log\\LoggerAwareTrait' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerAwareTrait.php',
        'Psr\\Log\\LoggerInterface' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerInterface.php',
        'Psr\\Log\\LoggerTrait' => __DIR__ . '/..' . '/psr/log/Psr/Log/LoggerTrait.php',
        'Psr\\Log\\NullLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/NullLogger.php',
        'Psr\\Log\\Test\\DummyTest' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/DummyTest.php',
        'Psr\\Log\\Test\\LoggerInterfaceTest' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
        'Psr\\Log\\Test\\TestLogger' => __DIR__ . '/..' . '/psr/log/Psr/Log/Test/TestLogger.php',
        'Psr\\SimpleCache\\CacheException' => __DIR__ . '/..' . '/psr/simple-cache/src/CacheException.php',
        'Psr\\SimpleCache\\CacheInterface' => __DIR__ . '/..' . '/psr/simple-cache/src/CacheInterface.php',
        'Psr\\SimpleCache\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/simple-cache/src/InvalidArgumentException.php',
        'app\\BaseController' => __DIR__ . '/../..' . '/app/BaseController.php',
        'app\\api\\controller\\WechatController' => __DIR__ . '/../..' . '/app/api/controller/WechatController.php',
        'app\\common\\service\\WechatService' => __DIR__ . '/../..' . '/app/common/service/WechatService.php',
        'think\\App' => __DIR__ . '/..' . '/topthink/framework/src/think/App.php',
        'think\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/Cache.php',
        'think\\Collection' => __DIR__ . '/..' . '/topthink/think-helper/src/Collection.php',
        'think\\Config' => __DIR__ . '/..' . '/topthink/framework/src/think/Config.php',
        'think\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/Console.php',
        'think\\Container' => __DIR__ . '/..' . '/topthink/framework/src/think/Container.php',
        'think\\Cookie' => __DIR__ . '/..' . '/topthink/framework/src/think/Cookie.php',
        'think\\Db' => __DIR__ . '/..' . '/topthink/framework/src/think/Db.php',
        'think\\DbManager' => __DIR__ . '/..' . '/topthink/think-orm/src/DbManager.php',
        'think\\Env' => __DIR__ . '/..' . '/topthink/framework/src/think/Env.php',
        'think\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/Event.php',
        'think\\Exception' => __DIR__ . '/..' . '/topthink/framework/src/think/Exception.php',
        'think\\Facade' => __DIR__ . '/..' . '/topthink/framework/src/think/Facade.php',
        'think\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/File.php',
        'think\\Http' => __DIR__ . '/..' . '/topthink/framework/src/think/Http.php',
        'think\\Lang' => __DIR__ . '/..' . '/topthink/framework/src/think/Lang.php',
        'think\\Log' => __DIR__ . '/..' . '/topthink/framework/src/think/Log.php',
        'think\\Manager' => __DIR__ . '/..' . '/topthink/framework/src/think/Manager.php',
        'think\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/Middleware.php',
        'think\\Model' => __DIR__ . '/..' . '/topthink/think-orm/src/Model.php',
        'think\\Paginator' => __DIR__ . '/..' . '/topthink/think-orm/src/Paginator.php',
        'think\\Pipeline' => __DIR__ . '/..' . '/topthink/framework/src/think/Pipeline.php',
        'think\\Request' => __DIR__ . '/..' . '/topthink/framework/src/think/Request.php',
        'think\\Response' => __DIR__ . '/..' . '/topthink/framework/src/think/Response.php',
        'think\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/Route.php',
        'think\\Service' => __DIR__ . '/..' . '/topthink/framework/src/think/Service.php',
        'think\\Session' => __DIR__ . '/..' . '/topthink/framework/src/think/Session.php',
        'think\\Validate' => __DIR__ . '/..' . '/topthink/framework/src/think/Validate.php',
        'think\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/View.php',
        'think\\cache\\Driver' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/Driver.php',
        'think\\cache\\TagSet' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/TagSet.php',
        'think\\cache\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/File.php',
        'think\\cache\\driver\\Memcache' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Memcache.php',
        'think\\cache\\driver\\Memcached' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Memcached.php',
        'think\\cache\\driver\\Redis' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Redis.php',
        'think\\cache\\driver\\Wincache' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Wincache.php',
        'think\\console\\Command' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Command.php',
        'think\\console\\Input' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Input.php',
        'think\\console\\Output' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Output.php',
        'think\\console\\Table' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Table.php',
        'think\\console\\command\\Clear' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Clear.php',
        'think\\console\\command\\Help' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Help.php',
        'think\\console\\command\\Lists' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Lists.php',
        'think\\console\\command\\Make' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Make.php',
        'think\\console\\command\\RouteList' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/RouteList.php',
        'think\\console\\command\\RunServer' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/RunServer.php',
        'think\\console\\command\\ServiceDiscover' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/ServiceDiscover.php',
        'think\\console\\command\\VendorPublish' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/VendorPublish.php',
        'think\\console\\command\\Version' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Version.php',
        'think\\console\\command\\make\\Command' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Command.php',
        'think\\console\\command\\make\\Controller' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Controller.php',
        'think\\console\\command\\make\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Event.php',
        'think\\console\\command\\make\\Listener' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Listener.php',
        'think\\console\\command\\make\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Middleware.php',
        'think\\console\\command\\make\\Model' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Model.php',
        'think\\console\\command\\make\\Service' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Service.php',
        'think\\console\\command\\make\\Subscribe' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Subscribe.php',
        'think\\console\\command\\make\\Validate' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Validate.php',
        'think\\console\\command\\optimize\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/optimize/Route.php',
        'think\\console\\command\\optimize\\Schema' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/optimize/Schema.php',
        'think\\console\\input\\Argument' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Argument.php',
        'think\\console\\input\\Definition' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Definition.php',
        'think\\console\\input\\Option' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Option.php',
        'think\\console\\output\\Ask' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Ask.php',
        'think\\console\\output\\Descriptor' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Descriptor.php',
        'think\\console\\output\\Formatter' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Formatter.php',
        'think\\console\\output\\Question' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Question.php',
        'think\\console\\output\\descriptor\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/descriptor/Console.php',
        'think\\console\\output\\driver\\Buffer' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Buffer.php',
        'think\\console\\output\\driver\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Console.php',
        'think\\console\\output\\driver\\Nothing' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Nothing.php',
        'think\\console\\output\\formatter\\Stack' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/formatter/Stack.php',
        'think\\console\\output\\formatter\\Style' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/formatter/Style.php',
        'think\\console\\output\\question\\Choice' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/question/Choice.php',
        'think\\console\\output\\question\\Confirmation' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/question/Confirmation.php',
        'think\\contract\\Arrayable' => __DIR__ . '/..' . '/topthink/think-helper/src/contract/Arrayable.php',
        'think\\contract\\CacheHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/CacheHandlerInterface.php',
        'think\\contract\\Jsonable' => __DIR__ . '/..' . '/topthink/think-helper/src/contract/Jsonable.php',
        'think\\contract\\LogHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/LogHandlerInterface.php',
        'think\\contract\\ModelRelationInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/ModelRelationInterface.php',
        'think\\contract\\SessionHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/SessionHandlerInterface.php',
        'think\\contract\\TemplateHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/TemplateHandlerInterface.php',
        'think\\db\\BaseBuilder' => __DIR__ . '/..' . '/topthink/think-orm/src/db/BaseBuilder.php',
        'think\\db\\BaseQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/BaseQuery.php',
        'think\\db\\Builder' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Builder.php',
        'think\\db\\CacheItem' => __DIR__ . '/..' . '/topthink/think-orm/src/db/CacheItem.php',
        'think\\db\\Connection' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Connection.php',
        'think\\db\\ConnectionInterface' => __DIR__ . '/..' . '/topthink/think-orm/src/db/ConnectionInterface.php',
        'think\\db\\Fetch' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Fetch.php',
        'think\\db\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Mongo.php',
        'think\\db\\PDOConnection' => __DIR__ . '/..' . '/topthink/think-orm/src/db/PDOConnection.php',
        'think\\db\\Query' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Query.php',
        'think\\db\\Raw' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Raw.php',
        'think\\db\\Where' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Where.php',
        'think\\db\\builder\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Mongo.php',
        'think\\db\\builder\\Mysql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Mysql.php',
        'think\\db\\builder\\Oracle' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Oracle.php',
        'think\\db\\builder\\Pgsql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Pgsql.php',
        'think\\db\\builder\\Sqlite' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Sqlite.php',
        'think\\db\\builder\\Sqlsrv' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Sqlsrv.php',
        'think\\db\\concern\\AggregateQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/AggregateQuery.php',
        'think\\db\\concern\\JoinAndViewQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/JoinAndViewQuery.php',
        'think\\db\\concern\\ModelRelationQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ModelRelationQuery.php',
        'think\\db\\concern\\ParamsBind' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ParamsBind.php',
        'think\\db\\concern\\ResultOperation' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ResultOperation.php',
        'think\\db\\concern\\TableFieldInfo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/TableFieldInfo.php',
        'think\\db\\concern\\TimeFieldQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/TimeFieldQuery.php',
        'think\\db\\concern\\Transaction' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/Transaction.php',
        'think\\db\\concern\\WhereQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/WhereQuery.php',
        'think\\db\\connector\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Mongo.php',
        'think\\db\\connector\\Mysql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Mysql.php',
        'think\\db\\connector\\Oracle' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Oracle.php',
        'think\\db\\connector\\Pgsql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Pgsql.php',
        'think\\db\\connector\\Sqlite' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Sqlite.php',
        'think\\db\\connector\\Sqlsrv' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Sqlsrv.php',
        'think\\db\\exception\\BindParamException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/BindParamException.php',
        'think\\db\\exception\\DataNotFoundException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DataNotFoundException.php',
        'think\\db\\exception\\DbEventException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DbEventException.php',
        'think\\db\\exception\\DbException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DbException.php',
        'think\\db\\exception\\InvalidArgumentException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/InvalidArgumentException.php',
        'think\\db\\exception\\ModelEventException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/ModelEventException.php',
        'think\\db\\exception\\ModelNotFoundException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/ModelNotFoundException.php',
        'think\\db\\exception\\PDOException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/PDOException.php',
        'think\\event\\AppInit' => __DIR__ . '/..' . '/topthink/framework/src/think/event/AppInit.php',
        'think\\event\\HttpEnd' => __DIR__ . '/..' . '/topthink/framework/src/think/event/HttpEnd.php',
        'think\\event\\HttpRun' => __DIR__ . '/..' . '/topthink/framework/src/think/event/HttpRun.php',
        'think\\event\\LogRecord' => __DIR__ . '/..' . '/topthink/framework/src/think/event/LogRecord.php',
        'think\\event\\LogWrite' => __DIR__ . '/..' . '/topthink/framework/src/think/event/LogWrite.php',
        'think\\event\\RouteLoaded' => __DIR__ . '/..' . '/topthink/framework/src/think/event/RouteLoaded.php',
        'think\\exception\\ClassNotFoundException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/ClassNotFoundException.php',
        'think\\exception\\ErrorException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/ErrorException.php',
        'think\\exception\\FileException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/FileException.php',
        'think\\exception\\FuncNotFoundException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/FuncNotFoundException.php',
        'think\\exception\\Handle' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/Handle.php',
        'think\\exception\\HttpException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/HttpException.php',
        'think\\exception\\HttpResponseException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/HttpResponseException.php',
        'think\\exception\\InvalidArgumentException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/InvalidArgumentException.php',
        'think\\exception\\RouteNotFoundException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/RouteNotFoundException.php',
        'think\\exception\\ValidateException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/ValidateException.php',
        'think\\facade\\App' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/App.php',
        'think\\facade\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Cache.php',
        'think\\facade\\Config' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Config.php',
        'think\\facade\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Console.php',
        'think\\facade\\Cookie' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Cookie.php',
        'think\\facade\\Db' => __DIR__ . '/..' . '/topthink/think-orm/src/facade/Db.php',
        'think\\facade\\Env' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Env.php',
        'think\\facade\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Event.php',
        'think\\facade\\Lang' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Lang.php',
        'think\\facade\\Log' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Log.php',
        'think\\facade\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Middleware.php',
        'think\\facade\\Request' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Request.php',
        'think\\facade\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Route.php',
        'think\\facade\\Session' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Session.php',
        'think\\facade\\Validate' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Validate.php',
        'think\\facade\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/View.php',
        'think\\file\\UploadedFile' => __DIR__ . '/..' . '/topthink/framework/src/think/file/UploadedFile.php',
        'think\\helper\\Arr' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Arr.php',
        'think\\helper\\Macroable' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Macroable.php',
        'think\\helper\\Str' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Str.php',
        'think\\initializer\\BootService' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/BootService.php',
        'think\\initializer\\Error' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/Error.php',
        'think\\initializer\\RegisterService' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/RegisterService.php',
        'think\\log\\Channel' => __DIR__ . '/..' . '/topthink/framework/src/think/log/Channel.php',
        'think\\log\\ChannelSet' => __DIR__ . '/..' . '/topthink/framework/src/think/log/ChannelSet.php',
        'think\\log\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/log/driver/File.php',
        'think\\log\\driver\\Socket' => __DIR__ . '/..' . '/topthink/framework/src/think/log/driver/Socket.php',
        'think\\middleware\\AllowCrossDomain' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/AllowCrossDomain.php',
        'think\\middleware\\CheckRequestCache' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/CheckRequestCache.php',
        'think\\middleware\\FormTokenCheck' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/FormTokenCheck.php',
        'think\\middleware\\LoadLangPack' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/LoadLangPack.php',
        'think\\middleware\\SessionInit' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/SessionInit.php',
        'think\\model\\Collection' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Collection.php',
        'think\\model\\Pivot' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Pivot.php',
        'think\\model\\Relation' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Relation.php',
        'think\\model\\concern\\Attribute' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Attribute.php',
        'think\\model\\concern\\AutoWriteId' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/AutoWriteId.php',
        'think\\model\\concern\\Conversion' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Conversion.php',
        'think\\model\\concern\\ModelEvent' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/ModelEvent.php',
        'think\\model\\concern\\OptimLock' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/OptimLock.php',
        'think\\model\\concern\\RelationShip' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/RelationShip.php',
        'think\\model\\concern\\SoftDelete' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/SoftDelete.php',
        'think\\model\\concern\\TimeStamp' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/TimeStamp.php',
        'think\\model\\concern\\Virtual' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Virtual.php',
        'think\\model\\contract\\EnumTransform' => __DIR__ . '/..' . '/topthink/think-orm/src/model/contract/EnumTransform.php',
        'think\\model\\contract\\FieldTypeTransform' => __DIR__ . '/..' . '/topthink/think-orm/src/model/contract/FieldTypeTransform.php',
        'think\\model\\relation\\BelongsTo' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/BelongsTo.php',
        'think\\model\\relation\\BelongsToMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/BelongsToMany.php',
        'think\\model\\relation\\HasMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasMany.php',
        'think\\model\\relation\\HasManyThrough' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasManyThrough.php',
        'think\\model\\relation\\HasOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasOne.php',
        'think\\model\\relation\\HasOneThrough' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasOneThrough.php',
        'think\\model\\relation\\MorphMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphMany.php',
        'think\\model\\relation\\MorphOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphOne.php',
        'think\\model\\relation\\MorphTo' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphTo.php',
        'think\\model\\relation\\MorphToMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphToMany.php',
        'think\\model\\relation\\OneToOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/OneToOne.php',
        'think\\paginator\\driver\\Bootstrap' => __DIR__ . '/..' . '/topthink/think-orm/src/paginator/driver/Bootstrap.php',
        'think\\response\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/response/File.php',
        'think\\response\\Html' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Html.php',
        'think\\response\\Json' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Json.php',
        'think\\response\\Jsonp' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Jsonp.php',
        'think\\response\\Redirect' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Redirect.php',
        'think\\response\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/response/View.php',
        'think\\response\\Xml' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Xml.php',
        'think\\route\\Dispatch' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Dispatch.php',
        'think\\route\\Domain' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Domain.php',
        'think\\route\\Resource' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Resource.php',
        'think\\route\\ResourceRegister' => __DIR__ . '/..' . '/topthink/framework/src/think/route/ResourceRegister.php',
        'think\\route\\Rule' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Rule.php',
        'think\\route\\RuleGroup' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleGroup.php',
        'think\\route\\RuleItem' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleItem.php',
        'think\\route\\RuleName' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleName.php',
        'think\\route\\Url' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Url.php',
        'think\\route\\dispatch\\Callback' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Callback.php',
        'think\\route\\dispatch\\Controller' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Controller.php',
        'think\\route\\dispatch\\Url' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Url.php',
        'think\\service\\ModelService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/ModelService.php',
        'think\\service\\PaginatorService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/PaginatorService.php',
        'think\\service\\ValidateService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/ValidateService.php',
        'think\\session\\Store' => __DIR__ . '/..' . '/topthink/framework/src/think/session/Store.php',
        'think\\session\\driver\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/session/driver/Cache.php',
        'think\\session\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/session/driver/File.php',
        'think\\validate\\ValidateRule' => __DIR__ . '/..' . '/topthink/framework/src/think/validate/ValidateRule.php',
        'think\\view\\driver\\Php' => __DIR__ . '/..' . '/topthink/framework/src/think/view/driver/Php.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit24995f66c8d2004af38c3df167f92cdf::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit24995f66c8d2004af38c3df167f92cdf::$prefixDirsPsr4;
            $loader->fallbackDirsPsr0 = ComposerStaticInit24995f66c8d2004af38c3df167f92cdf::$fallbackDirsPsr0;
            $loader->classMap = ComposerStaticInit24995f66c8d2004af38c3df167f92cdf::$classMap;

        }, null, ClassLoader::class);
    }
}
