<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序回复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007cba;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .btn {
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .config-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 小程序回复功能测试</h1>
        
        <div class="config-info">
            <h3>📋 功能说明</h3>
            <p>当用户发送"收到不支持的消息类型，暂无法显示"时，系统会自动回复小程序链接。</p>
            <ul>
                <li><strong>触发内容</strong>：收到不支持的消息类型，暂无法显示</li>
                <li><strong>小程序AppID</strong>：wxa484f0497f834791</li>
                <li><strong>页面路径</strong>：pages/gif/gif</li>
                <li><strong>参数</strong>：openid=用户的openid</li>
                <li><strong>回复类型</strong>：miniprogrampage（小程序卡片）</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>🧪 测试小程序回复</h3>
            <p>发送特定内容来触发小程序回复：</p>
            <label>用户OpenID:</label>
            <input type="text" id="test-openid" value="oft-jviMe0JPszvjOVASgnja8U0o" placeholder="输入测试用户的openid">
            <button class="btn" onclick="testMiniProgramReply()">发送触发消息</button>
            <button class="btn" onclick="testNormalMessage()">发送普通消息</button>
            <div id="test-result"></div>
        </div>
        
        <div class="section">
            <h3>📄 小程序消息XML格式</h3>
            <p>当触发条件满足时，系统会返回以下格式的XML：</p>
            <pre>&lt;xml&gt;
&lt;ToUserName&gt;&lt;![CDATA[用户openid]]&gt;&lt;/ToUserName&gt;
&lt;FromUserName&gt;&lt;![CDATA[公众号原始ID]]&gt;&lt;/FromUserName&gt;
&lt;CreateTime&gt;时间戳&lt;/CreateTime&gt;
&lt;MsgType&gt;&lt;![CDATA[miniprogrampage]]&gt;&lt;/MsgType&gt;
&lt;Title&gt;&lt;![CDATA[点击查看您的专属表情包]]&gt;&lt;/Title&gt;
&lt;AppId&gt;&lt;![CDATA[wxa484f0497f834791]]&gt;&lt;/AppId&gt;
&lt;PagePath&gt;&lt;![CDATA[pages/gif/gif?openid=用户openid]]&gt;&lt;/PagePath&gt;
&lt;ThumbMediaId&gt;&lt;![CDATA[]]&gt;&lt;/ThumbMediaId&gt;
&lt;/xml&gt;</pre>
        </div>
        
        <div class="section">
            <h3>🔍 测试其他消息类型</h3>
            <p>测试不同的消息内容：</p>
            <button class="btn" onclick="testEmojiMessage()">发送表情消息</button>
            <button class="btn" onclick="testImageMessage()">发送图片消息</button>
            <button class="btn" onclick="testCustomMessage()">自定义消息</button>
            <div style="margin-top: 10px;">
                <input type="text" id="custom-message" placeholder="输入自定义消息内容" value="你好，这是一个测试消息">
            </div>
            <div id="other-test-result"></div>
        </div>
        
        <div class="section">
            <h3>📊 日志查看</h3>
            <p>查看相关的日志记录：</p>
            <button class="btn" onclick="viewLogs()">查看系统日志</button>
            <div id="logs-result"></div>
        </div>
    </div>
    
    <script>
        async function testMiniProgramReply() {
            const openid = document.getElementById('test-openid').value.trim();
            const resultDiv = document.getElementById('test-result');
            
            if (!openid) {
                resultDiv.innerHTML = '<div class="result error">请输入用户OpenID</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result">正在测试小程序回复...</div>';
            
            const testXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[${openid}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[收到不支持的消息类型，暂无法显示]]></Content>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: testXml
                });
                
                const responseText = await response.text();
                
                // 检查是否返回了小程序消息
                const isMiniProgram = responseText.includes('miniprogrampage');
                const hasAppId = responseText.includes('wxa484f0497f834791');
                const hasPagePath = responseText.includes('pages/gif/gif');
                
                resultDiv.innerHTML = `
                    <div class="result ${isMiniProgram ? 'success' : 'error'}">
                        <h4>${isMiniProgram ? '✅ 小程序回复成功' : '❌ 未返回小程序消息'}</h4>
                        <p><strong>检测结果:</strong></p>
                        <ul>
                            <li>包含小程序类型: ${isMiniProgram ? '✅' : '❌'}</li>
                            <li>包含正确AppID: ${hasAppId ? '✅' : '❌'}</li>
                            <li>包含页面路径: ${hasPagePath ? '✅' : '❌'}</li>
                        </ul>
                        <p><strong>完整响应:</strong></p>
                        <pre>${responseText}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testNormalMessage() {
            const openid = document.getElementById('test-openid').value.trim();
            const resultDiv = document.getElementById('test-result');
            
            if (!openid) {
                resultDiv.innerHTML = '<div class="result error">请输入用户OpenID</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result">正在测试普通消息...</div>';
            
            const testXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[${openid}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[这是一个普通的文本消息]]></Content>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: testXml
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>✅ 普通消息测试完成</h4>
                        <p><strong>响应内容:</strong></p>
                        <pre>${responseText}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testEmojiMessage() {
            await sendTestMessage('你好 😊 这是一个包含表情的消息', 'other-test-result');
        }
        
        async function testImageMessage() {
            const openid = document.getElementById('test-openid').value.trim();
            const resultDiv = document.getElementById('other-test-result');
            
            const imageXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[${openid}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[image]]></MsgType>
<PicUrl><![CDATA[https://via.placeholder.com/300x200.jpg]]></PicUrl>
<MediaId><![CDATA[test_media_${Date.now()}]]></MediaId>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            await sendXmlMessage(imageXml, resultDiv, '图片消息');
        }
        
        async function testCustomMessage() {
            const customContent = document.getElementById('custom-message').value.trim();
            if (!customContent) {
                document.getElementById('other-test-result').innerHTML = '<div class="result error">请输入自定义消息内容</div>';
                return;
            }
            await sendTestMessage(customContent, 'other-test-result');
        }
        
        async function sendTestMessage(content, resultDivId) {
            const openid = document.getElementById('test-openid').value.trim();
            const resultDiv = document.getElementById(resultDivId);
            
            const testXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[${openid}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[${content}]]></Content>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            await sendXmlMessage(testXml, resultDiv, '文本消息');
        }
        
        async function sendXmlMessage(xmlContent, resultDiv, messageType) {
            resultDiv.innerHTML = `<div class="result">正在发送${messageType}...</div>`;
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: xmlContent
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>✅ ${messageType}发送成功</h4>
                        <p><strong>响应内容:</strong></p>
                        <pre>${responseText}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 发送失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function viewLogs() {
            const resultDiv = document.getElementById('logs-result');
            resultDiv.innerHTML = '<div class="result">正在获取日志...</div>';
            
            try {
                const response = await fetch('/api/wechat/debug/logs?type=all&lines=20');
                const data = await response.json();
                
                if (data.code === 200) {
                    let html = '<div class="result success"><h4>📋 最近日志记录</h4>';
                    
                    if (data.data.logs.thinkphp && data.data.logs.thinkphp.length > 0) {
                        // 过滤出与小程序相关的日志
                        const miniProgramLogs = data.data.logs.thinkphp.filter(log => 
                            log.includes('小程序') || log.includes('miniprogram') || log.includes('不支持的消息类型')
                        );
                        
                        if (miniProgramLogs.length > 0) {
                            html += '<h5>小程序相关日志:</h5><pre>';
                            miniProgramLogs.forEach(log => {
                                html += log + '\n';
                            });
                            html += '</pre>';
                        }
                        
                        html += '<h5>最近系统日志:</h5><pre>';
                        data.data.logs.thinkphp.slice(0, 10).forEach(log => {
                            html += log + '\n';
                        });
                        html += '</pre>';
                    } else {
                        html += '<p>暂无日志记录</p>';
                    }
                    
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ 获取日志失败</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
