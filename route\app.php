<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\Route;

// 首页
Route::get('/', 'Index/index');

// 测试接口
Route::get('test', 'Index/test');

// 微信公众号验证接口
Route::any('api/wechat/verify', 'app\api\controller\WechatController@verify');

// 微信素材库接口
Route::get('api/wechat/material/batch', 'app\api\controller\WechatController@batchGetMaterial');

// 图片管理接口
Route::get('api/images', 'app\api\controller\ImageController@index');
Route::get('api/images/stats', 'app\api\controller\ImageController@stats');
Route::get('images/manage', 'app\api\controller\ImageController@manage');

// 根据openid获取图片接口
Route::post('api/getImage', 'app\api\controller\ImageController@getImage');

// 微信调试接口
Route::get('api/wechat/debug', 'app\api\controller\WechatDebug@debugPage');
Route::get('api/wechat/debug/status', 'app\api\controller\WechatDebug@checkStatus');
Route::get('api/wechat/debug/simulate', 'app\api\controller\WechatDebug@simulateImageMessage');
Route::get('api/wechat/debug/download', 'app\api\controller\WechatDebug@testImageDownload');
Route::get('api/wechat/debug/logs', 'app\api\controller\WechatDebug@viewLogs');

// 测试页面路由
Route::get('test/material', function() {
    return response(file_get_contents(root_path() . 'public/test_material.html'))->contentType('text/html');
});
Route::get('test/auto-material', function() {
    return response(file_get_contents(root_path() . 'public/test_auto_material.html'))->contentType('text/html');
});
Route::get('test/getImage', function() {
    return response(file_get_contents(root_path() . 'public/test_getImage.html'))->contentType('text/html');
});
Route::get('test/miniprogram', function() {
    return response(file_get_contents(root_path() . 'public/test_miniprogram.html'))->contentType('text/html');
});
Route::get('test/rate-limit', function() {
    return response(file_get_contents(root_path() . 'public/test_rate_limit.html'))->contentType('text/html');
});
