<?php
// 微信公众号配置
return [
    // 微信公众号基本配置
    'token' => 'test_token_123',
    'app_id' => env('WECHAT_APP_ID', ''),
    'app_secret' => env('WECHAT_APP_SECRET', ''),
    'encoding_aes_key' => env('WECHAT_ENCODING_AES_KEY', ''),
    
    // 服务器配置
    'verify_url' => '/api/wechat/verify',

    // 自动获取素材库配置
    'auto_fetch_materials' => env('WECHAT_AUTO_FETCH_MATERIALS', true),
    
    // 日志配置
    'log' => [
        'enabled' => true,
        'level' => 'info',
        'file' => 'wechat.log'
    ],
    
    // 消息类型配置
    'message_types' => [
        'text' => 'text',
        'image' => 'image',
        'voice' => 'voice',
        'video' => 'video',
        'music' => 'music',
        'news' => 'news',
        'location' => 'location',
        'link' => 'link',
        'event' => 'event'
    ],
    
    // 事件类型配置
    'event_types' => [
        'subscribe' => 'subscribe',      // 关注事件
        'unsubscribe' => 'unsubscribe',  // 取消关注事件
        'click' => 'CLICK',              // 菜单点击事件
        'view' => 'VIEW',                // 菜单跳转链接事件
        'location' => 'LOCATION',        // 上报地理位置事件
        'scan' => 'SCAN'                 // 扫描二维码事件
    ]
];
