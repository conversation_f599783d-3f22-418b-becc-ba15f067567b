<?php
/**
 * 检查上传目录和权限
 */

$uploadDir = dirname(__DIR__) . '/public/upload/';
$results = [];

// 检查目录是否存在
$results['upload_dir_exists'] = is_dir($uploadDir);

// 尝试创建目录
if (!$results['upload_dir_exists']) {
    $results['create_dir_success'] = mkdir($uploadDir, 0755, true);
} else {
    $results['create_dir_success'] = true;
}

// 检查目录权限
$results['upload_dir_writable'] = is_writable($uploadDir);

// 尝试写入测试文件
$testFile = $uploadDir . 'test_' . time() . '.txt';
$testContent = 'This is a test file created at ' . date('Y-m-d H:i:s');
$results['write_test_file'] = file_put_contents($testFile, $testContent) !== false;

// 检查文件是否存在
$results['test_file_exists'] = file_exists($testFile);

// 读取文件内容
if ($results['test_file_exists']) {
    $results['test_file_content'] = file_get_contents($testFile);
    // 删除测试文件
    $results['delete_test_file'] = unlink($testFile);
}

// 创建子目录测试
$subDirs = ['normal', 'emoji'];
foreach ($subDirs as $subDir) {
    $subDirPath = $uploadDir . $subDir . '/';
    if (!is_dir($subDirPath)) {
        $results["create_{$subDir}_dir"] = mkdir($subDirPath, 0755, true);
    } else {
        $results["create_{$subDir}_dir"] = true;
    }
    $results["{$subDir}_dir_writable"] = is_writable($subDirPath);
}

// 检查PHP扩展
$extensions = ['curl', 'simplexml', 'json', 'mbstring', 'openssl'];
foreach ($extensions as $ext) {
    $results["ext_{$ext}"] = extension_loaded($ext);
}

// 检查PHP配置
$results['php_version'] = PHP_VERSION;
$results['max_execution_time'] = ini_get('max_execution_time');
$results['memory_limit'] = ini_get('memory_limit');
$results['upload_max_filesize'] = ini_get('upload_max_filesize');
$results['post_max_size'] = ini_get('post_max_size');

// 输出结果
header('Content-Type: application/json; charset=utf-8');
echo json_encode([
    'upload_dir' => $uploadDir,
    'timestamp' => date('Y-m-d H:i:s'),
    'results' => $results
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
