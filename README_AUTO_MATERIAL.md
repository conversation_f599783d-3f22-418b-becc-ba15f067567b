# 微信公众号自动获取素材库功能

## 功能概述

这个功能实现了在接收微信用户消息时自动获取素材库信息，并将详细数据保存到日志文件中。系统会智能控制获取频率，避免过于频繁的API调用。

## 🎯 主要特性

### 1. **自动触发机制**
- ✅ 当接收到微信用户消息时自动触发
- ✅ 智能频率控制：每10分钟最多获取一次
- ✅ 可通过配置开关控制启用/禁用

### 2. **图片素材获取**
- ✅ 仅获取图片素材 (image) 类型
- ✅ 通过接口参数控制获取数量
- ✅ 支持1-20个素材的获取范围

### 3. **详细日志记录**
- ✅ 系统日志：记录获取过程和状态
- ✅ 专用日志：`auto_material_fetch.log` 记录详细素材信息
- ✅ 结构化数据：JSON格式便于分析

## 📋 配置说明

### 环境变量配置

在 `.env` 文件中添加：

```env
# 微信公众号基本配置
WECHAT_APP_ID=your_app_id_here
WECHAT_APP_SECRET=your_app_secret_here
WECHAT_TOKEN=your_token_here

# 自动获取素材库开关
WECHAT_AUTO_FETCH_MATERIALS=true
```

### 详细配置 (config/wechat.php)

```php
// 自动获取素材库配置
'auto_fetch_materials' => env('WECHAT_AUTO_FETCH_MATERIALS', true),
```

### 获取数量控制

通过URL参数控制获取数量：

```
# 获取5个图片素材
/api/wechat/verify?material_count=5

# 获取20个图片素材
/api/wechat/verify?material_count=20

# 不指定参数，默认获取10个
/api/wechat/verify
```

## 🔄 工作流程

### 1. 消息接收流程
```
用户发送消息 → handleMessage() → autoFetchMaterials() → 获取素材库 → 记录日志
```

### 2. 频率控制机制
- 使用缓存锁控制获取频率
- 每10分钟最多获取一次
- 避免频繁调用微信API

### 3. 素材获取顺序
1. 检查功能开关
2. 检查频率限制
3. 获取AccessToken
4. 依次获取各类型素材
5. 记录详细日志

## 📊 日志记录

### 系统日志位置
```
runtime/log/202507/26.log
```

### 专用日志位置
```
runtime/log/auto_material_fetch.log
```

### 日志内容示例

#### 系统日志
```
[2024-07-26T23:30:00+08:00][info] 开始自动获取素材库信息
[2024-07-26T23:30:01+08:00][info] 获取AccessToken成功: ACCESS_TOKEN_123...
[2024-07-26T23:30:02+08:00][info] 正在获取image类型素材，数量: 5
[2024-07-26T23:30:03+08:00][info] 获取image素材成功 {"type":"image","total_count":50,"item_count":5}
```

#### 专用日志 (auto_material_fetch.log)
```json
{
    "trigger": "message_received",
    "timestamp": "2024-07-26 23:30:00",
    "summary": {
        "total_types": 3,
        "total_items": 10,
        "types_fetched": ["image", "news", "voice"]
    },
    "materials": {
        "image": {
            "total_count": 50,
            "item_count": 5,
            "item": [...]
        },
        "news": {
            "total_count": 20,
            "item_count": 3,
            "item": [...]
        }
    }
}
```

## 🧪 测试方法

### 1. 可视化测试页面
访问：`/test/auto-material`

功能包括：
- 发送模拟微信消息
- 查看自动获取日志
- 检查配置状态
- 实时监控功能

### 2. 手动测试步骤

1. **配置环境变量**
   ```bash
   # 在.env文件中设置
   WECHAT_AUTO_FETCH_MATERIALS=true
   WECHAT_APP_ID=your_app_id
   WECHAT_APP_SECRET=your_app_secret
   ```

2. **发送测试消息**
   ```bash
   # 发送测试消息（默认获取10个图片素材）
   curl -X POST http://yourdomain.com/api/wechat/verify \
        -H "Content-Type: application/xml" \
        -d '<xml><ToUserName><![CDATA[gh_test]]></ToUserName><FromUserName><![CDATA[test_user]]></FromUserName><CreateTime>1627296000</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[测试消息]]></Content><MsgId>123456</MsgId></xml>'

   # 发送测试消息并指定获取5个图片素材
   curl -X POST "http://yourdomain.com/api/wechat/verify?material_count=5" \
        -H "Content-Type: application/xml" \
        -d '<xml><ToUserName><![CDATA[gh_test]]></ToUserName><FromUserName><![CDATA[test_user]]></FromUserName><CreateTime>1627296000</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[测试消息]]></Content><MsgId>123456</MsgId></xml>'
   ```

3. **查看日志**
   ```bash
   # 查看系统日志
   tail -f runtime/log/202507/26.log
   
   # 查看专用日志
   tail -f runtime/log/auto_material_fetch.log
   ```

## ⚙️ 高级配置

### 1. 控制获取数量
通过URL参数控制获取数量：

```bash
# 获取5个图片素材
/api/wechat/verify?material_count=5

# 获取最大20个图片素材
/api/wechat/verify?material_count=20

# 不指定参数，使用默认值10个
/api/wechat/verify
```

### 2. 调整获取频率
修改 `shouldFetchMaterials()` 方法中的时间间隔：

```php
// 改为5分钟获取一次
if ($lastFetchTime && (time() - $lastFetchTime) < 300) {
    return false;
}
```

### 3. 添加条件触发
可以根据消息类型或内容决定是否触发：

```php
protected function shouldFetchMaterials($messageType = null): bool
{
    // 只在图片消息时触发
    if ($messageType && $messageType !== 'image') {
        return false;
    }
    
    // 原有的频率控制逻辑...
}
```

## 🔍 监控和调试

### 1. 实时监控
```bash
# 监控系统日志
tail -f runtime/log/$(date +%Y%m)/$(date +%d).log | grep "素材"

# 监控专用日志
tail -f runtime/log/auto_material_fetch.log
```

### 2. 调试工具
- **调试页面**：`/api/wechat/debug`
- **素材测试**：`/test/material`
- **自动获取测试**：`/test/auto-material`

### 3. 常见问题排查

#### 功能未触发
1. 检查 `WECHAT_AUTO_FETCH_MATERIALS` 是否为 `true`
2. 检查是否在频率限制期内
3. 查看系统日志是否有错误信息

#### AccessToken获取失败
1. 检查 `WECHAT_APP_ID` 和 `WECHAT_APP_SECRET` 配置
2. 确认网络能访问微信API
3. 检查公众号权限设置

#### 素材获取失败
1. 检查公众号是否有素材管理权限
2. 确认素材库中有对应类型的素材
3. 查看微信API返回的错误信息

## 📈 性能优化

### 1. 缓存优化
- AccessToken自动缓存2小时
- 频率控制使用缓存锁
- 避免重复获取相同素材

### 2. 请求优化
- 添加请求间隔避免频繁调用
- 批量获取多种类型素材
- 异步处理不阻塞消息响应

### 3. 日志优化
- 结构化日志便于分析
- 分离系统日志和业务日志
- 定期清理过期日志文件

## 🔒 安全注意事项

1. **API密钥保护**：不要在日志中记录完整的AccessToken
2. **频率限制**：严格控制API调用频率，避免被限制
3. **错误处理**：完善的异常处理，避免影响正常消息处理
4. **日志安全**：注意日志文件的访问权限

## 🚀 扩展功能

### 1. 素材分析
- 统计各类型素材数量变化
- 分析素材使用频率
- 生成素材报告

### 2. 智能获取
- 根据用户行为调整获取策略
- 预测性素材获取
- 个性化素材推荐

### 3. 数据同步
- 将素材信息同步到数据库
- 建立本地素材索引
- 实现素材搜索功能

现在你的系统已经具备了在接收微信消息时自动获取素材库的功能！
