<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>getImage接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007cba;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .image-preview {
            margin-top: 15px;
            text-align: center;
        }
        .image-preview img {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .api-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ getImage接口测试</h1>
        
        <div class="api-info">
            <h3>📋 接口信息</h3>
            <ul>
                <li><strong>接口地址</strong>: <code>POST /api/getImage</code></li>
                <li><strong>请求格式</strong>: <code>{"openid":"oft-jviMe0JPszvjOVASgnja8U0o"}</code></li>
                <li><strong>返回格式</strong>: <code>{"code":0, "imageUrl":"/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif"}</code></li>
                <li><strong>图片路径</strong>: <code>public/gif/{openid}.gif</code></li>
            </ul>
        </div>
        
        <div class="section">
            <h3>🧪 接口测试</h3>
            <div class="form-group">
                <label for="openid">OpenID:</label>
                <input type="text" id="openid" value="oft-jviMe0JPszvjOVASgnja8U0o" placeholder="输入微信用户的openid">
            </div>
            <button class="btn" onclick="testGetImage()">测试获取图片</button>
            <button class="btn" onclick="testWithEmptyOpenid()">测试空openid</button>
            <button class="btn" onclick="testWithInvalidOpenid()">测试无效openid</button>
            <button class="btn danger" onclick="clearResult()">清空结果</button>
            <div id="test-result"></div>
        </div>
        
        <div class="section">
            <h3>📁 文件管理</h3>
            <p>为了测试接口，需要确保gif目录存在并包含对应的图片文件。</p>
            <button class="btn" onclick="checkGifDirectory()">检查gif目录</button>
            <button class="btn" onclick="createTestImage()">创建测试图片</button>
            <div id="file-result"></div>
        </div>
        
        <div class="section">
            <h3>📊 批量测试</h3>
            <p>测试多个不同的openid：</p>
            <button class="btn" onclick="batchTest()">批量测试</button>
            <div id="batch-result"></div>
        </div>
        
        <div class="section">
            <h3>🔍 请求示例</h3>
            <h4>cURL命令:</h4>
            <pre>curl -X POST http://yourdomain.com/api/getImage \
  -H "Content-Type: application/json" \
  -d '{"openid":"oft-jviMe0JPszvjOVASgnja8U0o"}'</pre>
            
            <h4>JavaScript示例:</h4>
            <pre>fetch('/api/getImage', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    openid: 'oft-jviMe0JPszvjOVASgnja8U0o'
  })
})
.then(response => response.json())
.then(data => console.log(data));</pre>
        </div>
    </div>
    
    <script>
        async function testGetImage() {
            const openid = document.getElementById('openid').value.trim();
            const resultDiv = document.getElementById('test-result');
            
            if (!openid) {
                resultDiv.innerHTML = '<div class="result error">请输入openid</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result">正在测试...</div>';
            
            try {
                const response = await fetch('/api/getImage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ openid: openid })
                });
                
                const data = await response.json();
                
                let html = `
                    <div class="result ${data.code === 0 ? 'success' : 'error'}">
                        <h4>${data.code === 0 ? '✅ 请求成功' : '❌ 请求失败'}</h4>
                        <p><strong>返回数据:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                if (data.code === 0 && data.imageUrl) {
                    html += `
                        <div class="image-preview">
                            <p><strong>图片预览:</strong></p>
                            <img src="${data.imageUrl}" alt="用户图片" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display:none; color: #666; padding: 20px; border: 1px dashed #ccc;">图片加载失败或不存在</div>
                        </div>
                    `;
                }
                
                html += '</div>';
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求异常</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testWithEmptyOpenid() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="result">正在测试空openid...</div>';
            
            try {
                const response = await fetch('/api/getImage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ openid: '' })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="result ${data.code === 1 ? 'success' : 'error'}">
                        <h4>${data.code === 1 ? '✅ 正确处理空openid' : '❌ 未正确处理空openid'}</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求异常</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testWithInvalidOpenid() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="result">正在测试无效openid...</div>';
            
            const invalidOpenid = 'invalid_openid_' + Date.now();
            
            try {
                const response = await fetch('/api/getImage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ openid: invalidOpenid })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="result ${data.code === 1 ? 'success' : 'error'}">
                        <h4>${data.code === 1 ? '✅ 正确处理无效openid' : '❌ 未正确处理无效openid'}</h4>
                        <p><strong>测试openid:</strong> ${invalidOpenid}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求异常</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function clearResult() {
            document.getElementById('test-result').innerHTML = '';
            document.getElementById('file-result').innerHTML = '';
            document.getElementById('batch-result').innerHTML = '';
        }
        
        async function checkGifDirectory() {
            const resultDiv = document.getElementById('file-result');
            resultDiv.innerHTML = '<div class="result">正在检查gif目录...</div>';
            
            // 这里可以添加检查目录的逻辑
            resultDiv.innerHTML = `
                <div class="result">
                    <h4>📁 目录信息</h4>
                    <p><strong>预期路径:</strong> public/gif/</p>
                    <p><strong>图片格式:</strong> {openid}.gif</p>
                    <p><strong>示例文件:</strong> public/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif</p>
                    <p><strong>提示:</strong> 请确保gif目录存在并包含对应的图片文件</p>
                </div>
            `;
        }
        
        async function createTestImage() {
            const resultDiv = document.getElementById('file-result');
            resultDiv.innerHTML = `
                <div class="result">
                    <h4>🛠️ 创建测试图片</h4>
                    <p>要创建测试图片，请执行以下步骤：</p>
                    <ol>
                        <li>在项目根目录创建 <code>public/gif/</code> 目录</li>
                        <li>将测试图片重命名为 <code>oft-jviMe0JPszvjOVASgnja8U0o.gif</code></li>
                        <li>放置到 <code>public/gif/</code> 目录中</li>
                    </ol>
                    <p><strong>命令示例:</strong></p>
                    <pre>mkdir -p public/gif
# 将你的测试图片复制到指定位置
cp your_test_image.gif public/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif</pre>
                </div>
            `;
        }
        
        async function batchTest() {
            const resultDiv = document.getElementById('batch-result');
            resultDiv.innerHTML = '<div class="result">正在进行批量测试...</div>';
            
            const testOpenids = [
                'oft-jviMe0JPszvjOVASgnja8U0o',
                'test_openid_1',
                'test_openid_2',
                '',
                'invalid_openid_' + Date.now()
            ];
            
            let results = [];
            
            for (const openid of testOpenids) {
                try {
                    const response = await fetch('/api/getImage', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ openid: openid })
                    });
                    
                    const data = await response.json();
                    results.push({
                        openid: openid || '(空)',
                        success: response.ok,
                        data: data
                    });
                } catch (error) {
                    results.push({
                        openid: openid || '(空)',
                        success: false,
                        error: error.message
                    });
                }
            }
            
            let html = '<div class="result success"><h4>📊 批量测试结果</h4><ul>';
            results.forEach((result, index) => {
                const status = result.success && result.data.code === 0 ? '✅' : '❌';
                html += `<li>${status} OpenID: ${result.openid} - ${result.success ? JSON.stringify(result.data) : result.error}</li>`;
            });
            html += '</ul></div>';
            
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
