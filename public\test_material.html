<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信素材库测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007cba;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        select, input[type="number"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .btn {
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .material-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .material-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }
        .material-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
        }
        .material-info {
            margin-top: 10px;
            font-size: 12px;
        }
        .material-info p {
            margin: 2px 0;
        }
        .config-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 微信素材库测试工具</h1>
        
        <div class="config-section">
            <h3>⚠️ 配置提醒</h3>
            <p>使用此功能前，请确保在 <code>.env</code> 文件中配置了以下参数：</p>
            <ul>
                <li><code>WECHAT_APP_ID</code> - 微信公众号AppID</li>
                <li><code>WECHAT_APP_SECRET</code> - 微信公众号AppSecret</li>
            </ul>
        </div>
        
        <div class="form-section">
            <h3>🔧 素材库查询参数</h3>
            <div class="form-group">
                <label for="material-type">素材类型:</label>
                <select id="material-type">
                    <option value="image">图片 (image)</option>
                    <option value="voice">语音 (voice)</option>
                    <option value="video">视频 (video)</option>
                    <option value="thumb">缩略图 (thumb)</option>
                    <option value="news">图文消息 (news)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="offset">偏移量:</label>
                <input type="number" id="offset" value="0" min="0" placeholder="从第几个开始">
            </div>
            <div class="form-group">
                <label for="count">数量:</label>
                <input type="number" id="count" value="10" min="1" max="20" placeholder="获取数量(1-20)">
            </div>
            <button class="btn" onclick="getMaterials()">获取素材库</button>
            <button class="btn" onclick="clearResult()">清空结果</button>
        </div>
        
        <div class="form-section">
            <h3>📊 快速测试</h3>
            <button class="btn" onclick="testAllTypes()">测试所有类型</button>
            <button class="btn" onclick="getImageMaterials()">获取图片素材</button>
            <button class="btn" onclick="getNewsMaterials()">获取图文消息</button>
        </div>
        
        <div id="result-section"></div>
    </div>
    
    <script>
        async function getMaterials() {
            const type = document.getElementById('material-type').value;
            const offset = document.getElementById('offset').value;
            const count = document.getElementById('count').value;
            
            const resultSection = document.getElementById('result-section');
            resultSection.innerHTML = '<div class="result">正在获取素材库...</div>';
            
            try {
                const url = `/api/wechat/material/batch?type=${type}&offset=${offset}&count=${count}`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.code === 200) {
                    displaySuccess(data, type);
                } else {
                    displayError(data.message || '请求失败');
                }
            } catch (error) {
                displayError('请求异常: ' + error.message);
            }
        }
        
        function displaySuccess(data, type) {
            const resultSection = document.getElementById('result-section');
            
            let html = `
                <div class="result success">
                    <h4>✅ 获取成功</h4>
                    <p><strong>素材类型:</strong> ${type}</p>
                    <p><strong>总数量:</strong> ${data.data.total_count || 0}</p>
                    <p><strong>本次获取:</strong> ${data.data.item_count || 0}</p>
                </div>
            `;
            
            // 显示原始JSON数据
            html += `
                <div class="result">
                    <h4>📄 原始数据</h4>
                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                </div>
            `;
            
            // 如果有素材项目，显示可视化界面
            if (data.data.item && data.data.item.length > 0) {
                html += '<div class="result"><h4>🖼️ 素材预览</h4>';
                html += '<div class="material-grid">';
                
                data.data.item.forEach((item, index) => {
                    html += createMaterialCard(item, type, index);
                });
                
                html += '</div></div>';
            }
            
            resultSection.innerHTML = html;
        }
        
        function createMaterialCard(item, type, index) {
            let card = `<div class="material-item">`;
            
            // 根据类型显示不同内容
            switch (type) {
                case 'image':
                case 'thumb':
                    card += `
                        <img src="${item.url || ''}" alt="${item.name || 'Image'}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9vTwvdGV4dD48L3N2Zz4='">
                        <div class="material-info">
                            <p><strong>名称:</strong> ${item.name || 'N/A'}</p>
                            <p><strong>MediaID:</strong> ${item.media_id || 'N/A'}</p>
                            <p><strong>更新时间:</strong> ${new Date(item.update_time * 1000).toLocaleString()}</p>
                        </div>
                    `;
                    break;
                    
                case 'news':
                    if (item.content && item.content.news_item && item.content.news_item.length > 0) {
                        const news = item.content.news_item[0];
                        card += `
                            <img src="${news.thumb_url || ''}" alt="${news.title || 'News'}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvuaWh+a2iOaBrzwvdGV4dD48L3N2Zz4='>
                            <div class="material-info">
                                <p><strong>标题:</strong> ${news.title || 'N/A'}</p>
                                <p><strong>作者:</strong> ${news.author || 'N/A'}</p>
                                <p><strong>摘要:</strong> ${(news.digest || '').substring(0, 50)}...</p>
                                <p><strong>MediaID:</strong> ${item.media_id || 'N/A'}</p>
                                <p><strong>更新时间:</strong> ${new Date(item.update_time * 1000).toLocaleString()}</p>
                            </div>
                        `;
                    }
                    break;
                    
                default:
                    card += `
                        <div style="height: 120px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                            <span style="color: #666;">${type.toUpperCase()}</span>
                        </div>
                        <div class="material-info">
                            <p><strong>名称:</strong> ${item.name || 'N/A'}</p>
                            <p><strong>MediaID:</strong> ${item.media_id || 'N/A'}</p>
                            <p><strong>更新时间:</strong> ${new Date(item.update_time * 1000).toLocaleString()}</p>
                        </div>
                    `;
            }
            
            card += '</div>';
            return card;
        }
        
        function displayError(message) {
            const resultSection = document.getElementById('result-section');
            resultSection.innerHTML = `
                <div class="result error">
                    <h4>❌ 请求失败</h4>
                    <p>${message}</p>
                </div>
            `;
        }
        
        function clearResult() {
            document.getElementById('result-section').innerHTML = '';
        }
        
        async function testAllTypes() {
            const types = ['image', 'voice', 'video', 'thumb', 'news'];
            const resultSection = document.getElementById('result-section');
            
            resultSection.innerHTML = '<div class="result">正在测试所有素材类型...</div>';
            
            let results = [];
            
            for (const type of types) {
                try {
                    const response = await fetch(`/api/wechat/material/batch?type=${type}&offset=0&count=5`);
                    const data = await response.json();
                    results.push({
                        type: type,
                        success: data.code === 200,
                        data: data
                    });
                } catch (error) {
                    results.push({
                        type: type,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            let html = '<div class="result success"><h4>🧪 批量测试结果</h4><ul>';
            results.forEach(result => {
                const status = result.success ? '✅' : '❌';
                const count = result.success ? (result.data.data?.total_count || 0) : 0;
                html += `<li>${status} ${result.type}: ${result.success ? count + ' 个素材' : '失败'}</li>`;
            });
            html += '</ul></div>';
            
            html += '<div class="result"><h4>详细结果</h4><pre>' + JSON.stringify(results, null, 2) + '</pre></div>';
            
            resultSection.innerHTML = html;
        }
        
        function getImageMaterials() {
            document.getElementById('material-type').value = 'image';
            document.getElementById('count').value = '10';
            getMaterials();
        }
        
        function getNewsMaterials() {
            document.getElementById('material-type').value = 'news';
            document.getElementById('count').value = '5';
            getMaterials();
        }
    </script>
</body>
</html>
