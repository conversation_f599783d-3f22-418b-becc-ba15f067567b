<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\service\ImageService;

/**
 * 图片管理控制器
 */
class ImageController
{
    /**
     * 获取图片列表
     */
    public function index()
    {
        $type = input('type', ''); // emoji, normal, 或空字符串（全部）
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 20);
        
        $imageService = new ImageService();
        $result = $imageService->getImageList($type, $page, $limit);
        
        if ($result['success']) {
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'list' => $result['list'],
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit'],
                    'pages' => ceil($result['total'] / $result['limit'])
                ]
            ]);
        } else {
            return json([
                'code' => 500,
                'message' => $result['error'],
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取图片统计信息
     */
    public function stats()
    {
        $imageService = new ImageService();
        
        // 获取各类型图片数量
        $normalImages = $imageService->getImageList('normal', 1, 1);
        $emojiImages = $imageService->getImageList('emoji', 1, 1);
        $allImages = $imageService->getImageList('', 1, 1);
        
        // 计算总大小
        $totalSize = $this->calculateTotalSize();
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'total_images' => $allImages['total'],
                'normal_images' => $normalImages['total'],
                'emoji_images' => $emojiImages['total'],
                'total_size' => $totalSize,
                'total_size_formatted' => $this->formatFileSize($totalSize),
                'upload_path' => '/upload/',
                'last_update' => date('Y-m-d H:i:s')
            ]
        ]);
    }
    
    /**
     * 图片管理页面
     */
    public function manage()
    {
        $type = input('type', '');
        $page = (int)input('page', 1);
        $limit = 12; // 每页显示12张图片
        
        $imageService = new ImageService();
        $result = $imageService->getImageList($type, $page, $limit);
        
        $html = $this->generateManagePageHtml($result, $type, $page, $limit);
        
        return response($html)->contentType('text/html');
    }
    
    /**
     * 生成管理页面HTML
     */
    protected function generateManagePageHtml($result, $type, $page, $limit)
    {
        $list = $result['list'] ?? [];
        $total = $result['total'] ?? 0;
        $pages = ceil($total / $limit);
        
        $typeOptions = [
            '' => '全部图片',
            'normal' => '普通图片',
            'emoji' => '表情图片'
        ];
        
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信图片管理</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .filter { margin-bottom: 20px; }
        .filter select, .filter input { padding: 8px; margin-right: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-item { background: #fff; padding: 15px; border-radius: 8px; text-align: center; flex: 1; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 24px; font-weight: bold; color: #007cba; }
        .stat-label { color: #666; margin-top: 5px; }
        .image-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; }
        .image-item { background: #fff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .image-item img { width: 100%; height: 150px; object-fit: cover; }
        .image-info { padding: 15px; }
        .image-info h4 { margin: 0 0 10px 0; font-size: 14px; color: #333; }
        .image-info p { margin: 5px 0; font-size: 12px; color: #666; }
        .pagination { text-align: center; margin-top: 30px; }
        .pagination a { display: inline-block; padding: 8px 16px; margin: 0 4px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; }
        .pagination a.current { background: #005a87; }
        .pagination a:hover { background: #005a87; }
        .no-images { text-align: center; padding: 50px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>微信图片管理</h1>
        <div class="filter">
            <select onchange="filterImages(this.value)">
                <option value="">全部图片</option>
                <option value="normal"' . ($type === 'normal' ? ' selected' : '') . '>普通图片</option>
                <option value="emoji"' . ($type === 'emoji' ? ' selected' : '') . '>表情图片</option>
            </select>
        </div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">' . $total . '</div>
                <div class="stat-label">当前分类图片</div>
            </div>
        </div>
    </div>';
        
        if (empty($list)) {
            $html .= '<div class="no-images">暂无图片</div>';
        } else {
            $html .= '<div class="image-grid">';
            foreach ($list as $image) {
                $html .= '<div class="image-item">
                    <img src="' . htmlspecialchars($image['url']) . '" alt="' . htmlspecialchars($image['filename']) . '" loading="lazy">
                    <div class="image-info">
                        <h4>' . htmlspecialchars($image['filename']) . '</h4>
                        <p>大小: ' . $this->formatFileSize($image['size']) . '</p>
                        <p>时间: ' . $image['date'] . '</p>
                        <p><a href="' . htmlspecialchars($image['url']) . '" target="_blank">查看原图</a></p>
                    </div>
                </div>';
            }
            $html .= '</div>';
        }
        
        // 分页
        if ($pages > 1) {
            $html .= '<div class="pagination">';
            for ($i = 1; $i <= $pages; $i++) {
                $class = $i === $page ? ' class="current"' : '';
                $url = '?type=' . urlencode($type) . '&page=' . $i;
                $html .= '<a href="' . $url . '"' . $class . '>' . $i . '</a>';
            }
            $html .= '</div>';
        }
        
        $html .= '
    <script>
        function filterImages(type) {
            window.location.href = "?type=" + encodeURIComponent(type) + "&page=1";
        }
    </script>
</body>
</html>';
        
        return $html;
    }
    
    /**
     * 计算总文件大小
     */
    protected function calculateTotalSize()
    {
        $uploadDir = root_path() . 'public/upload/';
        if (!is_dir($uploadDir)) {
            return 0;
        }
        
        $totalSize = 0;
        $this->calculateDirSize($uploadDir, $totalSize);
        
        return $totalSize;
    }
    
    /**
     * 递归计算目录大小
     */
    protected function calculateDirSize($dir, &$totalSize)
    {
        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $filepath = $dir . $file;
            
            if (is_dir($filepath)) {
                $this->calculateDirSize($filepath . '/', $totalSize);
            } elseif (is_file($filepath)) {
                $totalSize += filesize($filepath);
            }
        }
    }
    
    /**
     * 格式化文件大小
     */
    protected function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 根据openid获取图片
     *
     * @return \think\Response
     */
    public function getImage()
    {
        try {
            // 获取POST请求的JSON数据
            $input = input('post.');
            $openid = $input['openid'] ?? '';

            // 验证openid参数
            if (empty($openid)) {
                return json([
                    'code' => 1,
                    'message' => 'openid参数不能为空',
                    'imageUrl' => ''
                ]);
            }

            // 记录请求日志
            trace('getImage请求: openid=' . $openid . ', request_time=' . date('Y-m-d H:i:s'));

            // 构建图片文件路径
            $imageUrl = $this->buildImageUrl($openid);

            // 检查图片文件是否存在
            $imagePath = $this->getImagePath($openid);

            if (!file_exists($imagePath)) {
                // 图片不存在，记录日志
                trace('图片文件不存在: openid=' . $openid . ', expected_path=' . $imagePath);

                return json([
                    'code' => 1,
                    'message' => '图片不存在',
                    'imageUrl' => ''
                ]);
            }

            // 记录成功日志
            trace('图片获取成功: openid=' . $openid . ', imageUrl=' . $imageUrl . ', file_size=' . filesize($imagePath));

            // 返回成功结果
            return json([
                'code' => 0,
                'imageUrl' => $imageUrl
            ]);

        } catch (\Exception $e) {
            // 记录异常日志
            trace('getImage异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

            return json([
                'code' => 1,
                'message' => '服务器内部错误',
                'imageUrl' => ''
            ]);
        }
    }

    /**
     * 构建图片URL
     *
     * @param string $openid
     * @return string
     */
    protected function buildImageUrl(string $openid): string
    {
        return "/gif/{$openid}.gif";
    }

    /**
     * 获取图片文件的实际路径
     *
     * @param string $openid
     * @return string
     */
    protected function getImagePath(string $openid): string
    {
        return root_path() . "public/gif/{$openid}.gif";
    }
}
