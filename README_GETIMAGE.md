# getImage接口文档

## 接口概述

`/api/getImage` 接口用于根据微信用户的openid获取对应的gif图片URL。

## 接口信息

- **请求地址**: `POST /api/getImage`
- **请求方式**: POST
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openid | string | 是 | 微信用户的openid |

### 请求示例

```json
{
    "openid": "oft-jviMe0JPszvjOVASgnja8U0o"
}
```

## 响应格式

### 成功响应

```json
{
    "code": 0,
    "imageUrl": "/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif"
}
```

### 失败响应

#### openid为空
```json
{
    "code": 1,
    "message": "openid参数不能为空",
    "imageUrl": ""
}
```

#### 图片不存在
```json
{
    "code": 1,
    "message": "图片不存在",
    "imageUrl": ""
}
```

#### 服务器错误
```json
{
    "code": 1,
    "message": "服务器内部错误",
    "imageUrl": ""
}
```

## 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 状态码，0表示成功，1表示失败 |
| message | string | 错误信息（仅失败时返回） |
| imageUrl | string | 图片URL路径 |

## 文件存储规则

- **存储目录**: `public/gif/`
- **文件命名**: `{openid}.gif`
- **文件格式**: GIF格式
- **访问路径**: `/gif/{openid}.gif`

### 示例
- openid: `oft-jviMe0JPszvjOVASgnja8U0o`
- 文件路径: `public/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif`
- 访问URL: `/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif`

## 使用示例

### cURL示例

```bash
curl -X POST http://yourdomain.com/api/getImage \
  -H "Content-Type: application/json" \
  -d '{"openid":"oft-jviMe0JPszvjOVASgnja8U0o"}'
```

### JavaScript示例

```javascript
async function getImage(openid) {
    try {
        const response = await fetch('/api/getImage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ openid: openid })
        });
        
        const data = await response.json();
        
        if (data.code === 0) {
            console.log('图片URL:', data.imageUrl);
            // 可以直接使用这个URL显示图片
            // <img src="${data.imageUrl}" alt="用户图片">
        } else {
            console.error('获取失败:', data.message);
        }
    } catch (error) {
        console.error('请求异常:', error);
    }
}

// 使用示例
getImage('oft-jviMe0JPszvjOVASgnja8U0o');
```

### PHP示例

```php
$openid = 'oft-jviMe0JPszvjOVASgnja8U0o';

$data = json_encode(['openid' => $openid]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://yourdomain.com/api/getImage');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

$result = json_decode($response, true);

if ($result['code'] === 0) {
    echo "图片URL: " . $result['imageUrl'];
} else {
    echo "获取失败: " . $result['message'];
}
```

## 测试工具

### 在线测试页面
访问 `/test/getImage` 可以使用可视化界面测试接口。

测试页面功能：
- 输入openid测试获取图片
- 测试空openid的处理
- 测试无效openid的处理
- 批量测试多个openid
- 查看请求和响应示例

### 准备测试数据

1. **创建gif目录**:
   ```bash
   mkdir -p public/gif
   ```

2. **添加测试图片**:
   ```bash
   # 将你的gif图片重命名并放置到指定位置
   cp your_test_image.gif public/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif
   ```

3. **设置权限**:
   ```bash
   chmod 755 public/gif
   chmod 644 public/gif/*.gif
   ```

## 日志记录

接口会记录以下日志信息：

### 成功请求日志
```
[2024-07-26T23:30:00+08:00][info] getImage请求 {"openid":"oft-jviMe0JPszvjOVASgnja8U0o","request_time":"2024-07-26 23:30:00"}
[2024-07-26T23:30:00+08:00][info] 图片获取成功 {"openid":"oft-jviMe0JPszvjOVASgnja8U0o","imageUrl":"/gif/oft-jviMe0JPszvjOVASgnja8U0o.gif","file_size":12345}
```

### 失败请求日志
```
[2024-07-26T23:30:00+08:00][info] getImage请求 {"openid":"invalid_openid","request_time":"2024-07-26 23:30:00"}
[2024-07-26T23:30:00+08:00][info] 图片文件不存在 {"openid":"invalid_openid","expected_path":"/path/to/public/gif/invalid_openid.gif"}
```

### 异常日志
```
[2024-07-26T23:30:00+08:00][info] getImage异常 {"error":"错误信息","file":"/path/to/file.php","line":123}
```

## 错误处理

接口包含完善的错误处理机制：

1. **参数验证**: 检查openid是否为空
2. **文件检查**: 验证对应的gif文件是否存在
3. **异常捕获**: 捕获并记录所有异常情况
4. **日志记录**: 详细记录请求和处理过程

## 性能考虑

1. **文件检查**: 使用`file_exists()`快速检查文件是否存在
2. **路径构建**: 使用固定的路径模式，避免复杂的路径解析
3. **日志记录**: 使用ThinkPHP的trace()函数进行高效日志记录
4. **错误处理**: 快速失败，避免不必要的处理

## 安全注意事项

1. **路径安全**: 固定的文件路径格式，防止路径遍历攻击
2. **参数验证**: 严格验证输入参数
3. **文件类型**: 限制为gif格式文件
4. **访问控制**: 可以根据需要添加访问权限控制

## 扩展功能

基于此接口，可以扩展以下功能：

1. **多格式支持**: 支持jpg、png等其他图片格式
2. **缓存机制**: 添加图片URL缓存，提高响应速度
3. **图片处理**: 支持图片缩放、裁剪等处理
4. **批量获取**: 支持一次请求获取多个用户的图片
5. **统计功能**: 记录图片访问统计信息
