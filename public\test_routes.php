<?php
/**
 * 路由测试脚本
 * 用于验证所有路由是否正确配置
 */

$routes = [
    'GET /' => '首页',
    'GET /test' => '测试接口',
    'POST /api/wechat/verify' => '微信验证接口',
    'GET /api/wechat/material/batch' => '微信素材库接口',
    'GET /api/images' => '图片列表接口',
    'GET /api/images/stats' => '图片统计接口',
    'GET /images/manage' => '图片管理页面',
    'POST /api/getImage' => 'getImage接口',
    'GET /api/wechat/debug' => '微信调试页面',
    'GET /test/getImage' => 'getImage测试页面'
];

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>路由测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #007cba; text-align: center; }
        .route-list { list-style: none; padding: 0; }
        .route-item { margin: 10px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 5px; background: #fafafa; }
        .route-method { display: inline-block; padding: 3px 8px; border-radius: 3px; font-weight: bold; color: white; margin-right: 10px; }
        .get { background: #28a745; }
        .post { background: #007cba; }
        .any { background: #6c757d; }
        .route-url { font-family: monospace; font-weight: bold; }
        .route-desc { color: #666; margin-left: 10px; }
        .test-btn { padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; margin-left: 10px; }
        .test-btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🛣️ 路由测试页面</h1>
        <p>以下是当前配置的所有路由：</p>
        <ul class='route-list'>";

foreach ($routes as $route => $description) {
    list($method, $url) = explode(' ', $route, 2);
    $methodClass = strtolower($method);
    
    echo "<li class='route-item'>
        <span class='route-method {$methodClass}'>{$method}</span>
        <span class='route-url'>{$url}</span>
        <span class='route-desc'>{$description}</span>";
    
    // 为GET路由添加测试按钮
    if ($method === 'GET') {
        echo "<button class='test-btn' onclick='testRoute(\"{$url}\")'>测试</button>";
    }
    
    echo "</li>";
}

echo "        </ul>
        
        <h3>🧪 POST接口测试</h3>
        <div style='margin: 20px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 5px; background: #fafafa;'>
            <h4>测试 /api/getImage 接口</h4>
            <input type='text' id='openid' placeholder='输入openid' value='oft-jviMe0JPszvjOVASgnja8U0o' style='padding: 8px; width: 300px; margin-right: 10px;'>
            <button class='test-btn' onclick='testGetImage()'>测试getImage</button>
            <div id='result' style='margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; display: none;'></div>
        </div>
        
        <h3>📋 控制器文件检查</h3>
        <div style='margin: 20px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 5px; background: #fafafa;'>
            <p>当前控制器文件：</p>
            <ul>
                <li>✅ app/api/controller/WechatController.php</li>
                <li>✅ app/api/controller/ImageController.php</li>
                <li>✅ app/api/controller/WechatDebug.php</li>
                <li>✅ app/api/controller/Wechat.php</li>
            </ul>
        </div>
    </div>
    
    <script>
        function testRoute(url) {
            window.open(url, '_blank');
        }
        
        async function testGetImage() {
            const openid = document.getElementById('openid').value;
            const resultDiv = document.getElementById('result');
            
            if (!openid) {
                alert('请输入openid');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await fetch('/api/getImage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ openid: openid })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>响应结果:</strong><br>
                    <pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; margin-top: 5px;'>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<strong style='color: red;'>请求失败:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>";
?>
