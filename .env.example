# 应用配置
APP_DEBUG = true
APP_TRACE = false

# 数据库配置
DATABASE_TYPE = mysql
DATABASE_HOSTNAME = 127.0.0.1
DATABASE_DATABASE = your_database_name
DATABASE_USERNAME = your_username
DATABASE_PASSWORD = your_password
DATABASE_HOSTPORT = 3306
DATABASE_CHARSET = utf8mb4

# 微信公众号配置
# 在微信公众平台后台设置的Token
WECHAT_TOKEN = your_wechat_token_here

# 微信公众号的AppID
WECHAT_APP_ID = your_app_id_here

# 微信公众号的AppSecret
WECHAT_APP_SECRET = your_app_secret_here

# 消息加解密密钥（如果启用了消息加解密）
WECHAT_ENCODING_AES_KEY = your_encoding_aes_key_here

# 是否启用自动获取素材库功能（接收消息时自动获取）
WECHAT_AUTO_FETCH_MATERIALS = true

# 缓存配置
CACHE_DRIVER = file

# 日志配置
LOG_CHANNEL = file
