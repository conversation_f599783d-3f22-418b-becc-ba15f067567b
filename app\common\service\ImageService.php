<?php
declare(strict_types=1);

namespace app\common\service;

/**
 * 图片处理服务类
 */
class ImageService
{
    /**
     * 允许的图片类型
     */
    const ALLOWED_TYPES = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    
    /**
     * 最大文件大小 (5MB)
     */
    const MAX_FILE_SIZE = 5 * 1024 * 1024;
    
    /**
     * 下载并保存微信图片
     * 
     * @param string $picUrl 图片URL
     * @param string $fromUser 用户标识
     * @param string $type 图片类型 (emoji/normal)
     * @return array
     */
    public function downloadWechatImage(string $picUrl, string $fromUser, string $type = 'normal'): array
    {
        try {
            trace('ImageService: 开始处理图片', [
                'url' => $picUrl,
                'user' => $fromUser,
                'type' => $type
            ]);

            // 验证URL
            if (!filter_var($picUrl, FILTER_VALIDATE_URL)) {
                trace('ImageService: URL验证失败 - ' . $picUrl);
                return $this->errorResponse('无效的图片URL');
            }
            trace('ImageService: URL验证通过');

            // 创建保存目录
            $uploadPath = $this->createUploadPath($type);
            if (!$uploadPath['success']) {
                trace('ImageService: 创建目录失败', $uploadPath);
                return $uploadPath;
            }
            trace('ImageService: 目录创建成功 - ' . $uploadPath['path']);

            // 下载图片
            trace('ImageService: 开始下载图片 - ' . $picUrl);
            $imageData = $this->downloadImage($picUrl);
            if (!$imageData['success']) {
                trace('ImageService: 图片下载失败', $imageData);
                return $imageData;
            }
            trace('ImageService: 图片下载成功，大小: ' . strlen($imageData['data']) . ' bytes');
            
            // 验证图片
            trace('ImageService: 开始验证图片数据');
            $validation = $this->validateImage($imageData['data']);
            if (!$validation['success']) {
                trace('ImageService: 图片验证失败', $validation);
                return $validation;
            }
            trace('ImageService: 图片验证通过，类型: ' . $validation['extension']);

            // 生成文件名和路径
            $filename = $this->generateFilename($fromUser, $validation['extension']);
            $filepath = $uploadPath['path'] . $filename;
            trace('ImageService: 生成文件路径 - ' . $filepath);

            // 保存图片
            trace('ImageService: 开始保存图片到文件系统');
            if (file_put_contents($filepath, $imageData['data']) === false) {
                trace('ImageService: 文件保存失败 - ' . $filepath);
                return $this->errorResponse('图片保存失败');
            }
            trace('ImageService: 图片保存成功');
            
            // 生成访问URL
            $accessUrl = $this->generateAccessUrl($uploadPath['relative'] . $filename);
            
            // 记录保存信息
            $this->logImageSave([
                'user' => $fromUser,
                'type' => $type,
                'original_url' => $picUrl,
                'saved_path' => $filepath,
                'access_url' => $accessUrl,
                'filename' => $filename,
                'size' => strlen($imageData['data']),
                'time' => date('Y-m-d H:i:s')
            ]);
            
            return $this->successResponse([
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $accessUrl,
                'size' => strlen($imageData['data']),
                'type' => $type
            ]);
            
        } catch (\Exception $e) {
            return $this->errorResponse('图片处理异常: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建上传目录
     */
    protected function createUploadPath(string $type): array
    {
        $baseDir = root_path() . 'public/upload/';
        $typeDir = $type . '/';
        $dateDir = date('Y/m/d') . '/';
        
        $fullPath = $baseDir . $typeDir . $dateDir;
        $relativePath = 'upload/' . $typeDir . $dateDir;
        
        if (!is_dir($fullPath)) {
            if (!mkdir($fullPath, 0755, true)) {
                return $this->errorResponse('无法创建上传目录');
            }
        }
        
        return $this->successResponse([
            'path' => $fullPath,
            'relative' => $relativePath
        ]);
    }
    
    /**
     * 下载图片
     */
    protected function downloadImage(string $url): array
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; WechatBot/1.0)',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($data === false || !empty($error)) {
            return $this->errorResponse('图片下载失败: ' . $error);
        }
        
        if ($httpCode !== 200) {
            return $this->errorResponse('图片下载失败，HTTP状态码: ' . $httpCode);
        }
        
        return $this->successResponse(['data' => $data]);
    }
    
    /**
     * 验证图片数据
     */
    protected function validateImage(string $imageData): array
    {
        // 检查文件大小
        if (strlen($imageData) > self::MAX_FILE_SIZE) {
            return $this->errorResponse('图片文件过大，最大支持5MB');
        }
        
        if (strlen($imageData) < 100) {
            return $this->errorResponse('图片文件过小，可能不是有效图片');
        }
        
        // 通过文件头判断图片类型
        $imageInfo = $this->getImageTypeFromData($imageData);
        if (!$imageInfo['success']) {
            return $imageInfo;
        }
        
        return $this->successResponse([
            'extension' => $imageInfo['extension'],
            'mime_type' => $imageInfo['mime_type']
        ]);
    }
    
    /**
     * 从图片数据获取图片类型
     */
    protected function getImageTypeFromData(string $data): array
    {
        $header = substr($data, 0, 20);
        
        // JPEG
        if (substr($header, 0, 3) === "\xFF\xD8\xFF") {
            return $this->successResponse([
                'extension' => 'jpg',
                'mime_type' => 'image/jpeg'
            ]);
        }
        
        // PNG
        if (substr($header, 0, 8) === "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A") {
            return $this->successResponse([
                'extension' => 'png',
                'mime_type' => 'image/png'
            ]);
        }
        
        // GIF
        if (substr($header, 0, 6) === "GIF87a" || substr($header, 0, 6) === "GIF89a") {
            return $this->successResponse([
                'extension' => 'gif',
                'mime_type' => 'image/gif'
            ]);
        }
        
        // WebP
        if (substr($header, 0, 4) === "RIFF" && substr($header, 8, 4) === "WEBP") {
            return $this->successResponse([
                'extension' => 'webp',
                'mime_type' => 'image/webp'
            ]);
        }
        
        // 默认为JPEG
        return $this->successResponse([
            'extension' => 'jpg',
            'mime_type' => 'image/jpeg'
        ]);
    }
    
    /**
     * 生成文件名
     */
    protected function generateFilename(string $fromUser, string $extension): string
    {
        $userHash = substr(md5($fromUser), 0, 8);
        $timestamp = time();
        $random = uniqid();
        
        return "wechat_{$userHash}_{$timestamp}_{$random}.{$extension}";
    }
    
    /**
     * 生成访问URL
     */
    protected function generateAccessUrl(string $relativePath): string
    {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        return $protocol . '://' . $host . '/' . ltrim($relativePath, '/');
    }
    
    /**
     * 记录图片保存日志
     */
    protected function logImageSave(array $data): void
    {
        $logFile = root_path() . 'runtime/log/image_saves.log';
        $logData = json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 成功响应
     */
    protected function successResponse(array $data = []): array
    {
        return array_merge(['success' => true], $data);
    }
    
    /**
     * 错误响应
     */
    protected function errorResponse(string $message): array
    {
        return [
            'success' => false,
            'error' => $message
        ];
    }
    
    /**
     * 获取图片列表
     */
    public function getImageList(string $type = '', int $page = 1, int $limit = 20): array
    {
        $uploadDir = root_path() . 'public/upload/';
        
        if (!empty($type)) {
            $uploadDir .= $type . '/';
        }
        
        if (!is_dir($uploadDir)) {
            return $this->successResponse(['list' => [], 'total' => 0]);
        }
        
        $images = [];
        $this->scanImages($uploadDir, $images);
        
        // 按时间倒序排序
        usort($images, function($a, $b) {
            return $b['time'] <=> $a['time'];
        });
        
        $total = count($images);
        $offset = ($page - 1) * $limit;
        $list = array_slice($images, $offset, $limit);
        
        return $this->successResponse([
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }
    
    /**
     * 递归扫描图片文件
     */
    protected function scanImages(string $dir, array &$images): void
    {
        $files = scandir($dir);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $filepath = $dir . $file;
            
            if (is_dir($filepath)) {
                $this->scanImages($filepath . '/', $images);
            } elseif (is_file($filepath)) {
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                if (in_array($extension, self::ALLOWED_TYPES)) {
                    $relativePath = str_replace(root_path() . 'public/', '', $filepath);
                    $images[] = [
                        'filename' => $file,
                        'path' => $filepath,
                        'url' => $this->generateAccessUrl($relativePath),
                        'size' => filesize($filepath),
                        'time' => filemtime($filepath),
                        'date' => date('Y-m-d H:i:s', filemtime($filepath))
                    ];
                }
            }
        }
    }
}
