# 微信公众号频率限制功能

## 功能概述

为了防止用户刷屏和过度使用系统资源，微信公众号添加了消息发送频率限制功能。当用户发送消息过于频繁时，系统会回复"您手速过快，请3s发送一次"。

## 🎯 功能特性

### 1. **智能频率检测**
- ✅ 基于用户openid的个性化限制
- ✅ 3秒内只能发送一条消息
- ✅ 适用于文本消息和图片消息

### 2. **缓存机制**
- ✅ 使用ThinkPHP缓存系统
- ✅ 缓存时间10秒，足够频率控制
- ✅ 自动清理过期数据

### 3. **用户友好提示**
- ✅ 明确的限制提示信息
- ✅ 不影响正常使用体验
- ✅ 详细的日志记录

## 📋 技术实现

### 核心方法

#### 1. 频率检查方法
```php
protected function isUserSendingTooFast(string $fromUser): bool
{
    $cacheKey = 'user_last_send_time_' . md5($fromUser);
    $lastSendTime = cache($cacheKey);
    
    if ($lastSendTime && (time() - $lastSendTime) < 3) { // 3秒限制
        return true;
    }
    
    return false;
}
```

#### 2. 时间记录方法
```php
protected function recordUserLastSendTime(string $fromUser): void
{
    $cacheKey = 'user_last_send_time_' . md5($fromUser);
    cache($cacheKey, time(), 10); // 缓存10秒
}
```

### 集成位置

频率限制已集成到以下消息处理方法中：

1. **`handleTextMessage()`** - 文本消息处理
2. **`handleImageMessage()`** - 图片消息处理

### 处理流程

```
用户发送消息
    ↓
检查发送频率 (isUserSendingTooFast)
    ↓
如果过于频繁 → 返回限制提示
    ↓
如果正常 → 记录发送时间 → 正常处理消息
```

## ⚙️ 配置参数

### 可调整的参数

| 参数 | 当前值 | 说明 | 修改位置 |
|------|--------|------|----------|
| 限制时间间隔 | 3秒 | 两次发送之间的最小间隔 | `isUserSendingTooFast()` 方法 |
| 缓存时间 | 10秒 | 用户发送时间的缓存时长 | `recordUserLastSendTime()` 方法 |
| 提示消息 | "您手速过快，请3s发送一次" | 触发限制时的回复内容 | 各消息处理方法 |

### 自定义配置示例

#### 修改限制时间间隔为5秒
```php
if ($lastSendTime && (time() - $lastSendTime) < 5) { // 改为5秒
    return true;
}
```

#### 修改提示消息
```php
return $this->replyTextMessage($message, "发送太快了，请稍等片刻再试");
```

#### 不同消息类型使用不同限制
```php
protected function getMessageRateLimit(string $msgType): int
{
    $limits = [
        'text' => 3,  // 文本消息3秒限制
        'image' => 5, // 图片消息5秒限制
        'voice' => 10 // 语音消息10秒限制
    ];
    
    return $limits[$msgType] ?? 3;
}
```

## 🧪 测试方法

### 1. 可视化测试页面
访问：`/test/rate-limit`

测试功能包括：
- 单次发送测试
- 快速连发测试
- 批量发送测试
- 间隔发送测试
- 实时倒计时显示

### 2. 手动测试步骤

#### 基本频率限制测试
1. 向公众号发送一条消息
2. 立即再发送一条消息
3. 应该收到"您手速过快，请3s发送一次"的回复

#### 正常发送测试
1. 向公众号发送一条消息
2. 等待4秒后再发送一条消息
3. 应该正常处理第二条消息

### 3. 命令行测试
```bash
# 第一条消息
curl -X POST http://yourdomain.com/api/wechat/verify \
  -H "Content-Type: application/xml" \
  -d '<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[test_user_123]]></FromUserName>
<CreateTime>1627296000</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[第一条消息]]></Content>
<MsgId>123456</MsgId>
</xml>'

# 立即发送第二条消息（应该被限制）
curl -X POST http://yourdomain.com/api/wechat/verify \
  -H "Content-Type: application/xml" \
  -d '<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[test_user_123]]></FromUserName>
<CreateTime>1627296001</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[第二条消息]]></Content>
<MsgId>123457</MsgId>
</xml>'
```

## 📊 日志记录

### 日志内容
系统会记录以下频率限制相关的日志：

```
[2024-07-26T23:30:00+08:00][info] 用户发送过于频繁: test_user_123
[2024-07-26T23:30:00+08:00][info] 用户发送图片过于频繁: test_user_456
```

### 查看日志
```bash
# 查看实时日志
tail -f runtime/log/202507/26.log

# 过滤频率限制相关日志
tail -f runtime/log/202507/26.log | grep "过于频繁"
```

## 🔍 故障排除

### 常见问题

#### 1. 频率限制不生效
**可能原因**：
- 缓存系统未正常工作
- 用户openid获取错误
- 时间计算有误

**解决方法**：
- 检查ThinkPHP缓存配置
- 验证openid是否正确获取
- 查看系统时间是否准确

#### 2. 误判正常用户
**可能原因**：
- 限制时间设置过长
- 缓存未及时清理
- 多个用户使用相同openid

**解决方法**：
- 调整限制时间间隔
- 检查缓存清理机制
- 确保openid唯一性

#### 3. 缓存占用过多内存
**可能原因**：
- 缓存时间设置过长
- 大量用户同时使用

**解决方法**：
- 减少缓存时间
- 使用Redis等外部缓存
- 定期清理过期缓存

### 调试方法

#### 1. 启用详细日志
```php
$this->wechatService->log('频率检查: 用户=' . $fromUser . ', 上次时间=' . $lastSendTime . ', 当前时间=' . time());
```

#### 2. 查看缓存状态
```php
$cacheKey = 'user_last_send_time_' . md5($fromUser);
$lastTime = cache($cacheKey);
$this->wechatService->log('缓存状态: key=' . $cacheKey . ', value=' . $lastTime);
```

#### 3. 手动清理缓存
```php
// 清理特定用户的频率限制缓存
$cacheKey = 'user_last_send_time_' . md5($fromUser);
cache($cacheKey, null);
```

## 🚀 扩展功能

### 1. 分级频率限制
根据用户类型设置不同的限制：
```php
protected function getUserRateLimit(string $fromUser): int
{
    // VIP用户1秒限制，普通用户3秒限制
    if ($this->isVipUser($fromUser)) {
        return 1;
    }
    return 3;
}
```

### 2. 动态调整限制
根据系统负载动态调整：
```php
protected function getDynamicRateLimit(): int
{
    $systemLoad = $this->getSystemLoad();
    if ($systemLoad > 80) {
        return 10; // 高负载时增加限制
    }
    return 3; // 正常负载
}
```

### 3. 白名单功能
为特定用户设置白名单：
```php
protected function isWhitelistUser(string $fromUser): bool
{
    $whitelist = ['admin_user', 'test_user'];
    return in_array($fromUser, $whitelist);
}
```

### 4. 统计分析
记录频率限制的触发统计：
```php
protected function recordRateLimitStats(string $fromUser): void
{
    $statsKey = 'rate_limit_stats_' . date('Y-m-d');
    $stats = cache($statsKey) ?: [];
    $stats[$fromUser] = ($stats[$fromUser] ?? 0) + 1;
    cache($statsKey, $stats, 86400); // 缓存一天
}
```

## 📱 用户体验优化

### 1. 渐进式提示
根据触发次数给出不同提示：
```php
protected function getRateLimitMessage(string $fromUser): string
{
    $count = $this->getRateLimitCount($fromUser);
    
    if ($count == 1) {
        return "您手速过快，请3s发送一次";
    } elseif ($count < 5) {
        return "请稍等片刻再发送消息哦~";
    } else {
        return "检测到频繁操作，请稍后再试";
    }
}
```

### 2. 倒计时提示
告诉用户还需等待多长时间：
```php
$remainingTime = 3 - (time() - $lastSendTime);
return "请等待 {$remainingTime} 秒后再发送";
```

### 3. 友好的错误页面
为频繁操作的用户提供帮助信息：
```php
if ($this->isFrequentUser($fromUser)) {
    return "看起来您很活跃！为了更好的体验，建议您：\n1. 适当放慢发送速度\n2. 一次性发送完整信息\n3. 如有问题可联系客服";
}
```

现在你的微信公众号已经具备了完善的频率限制功能！用户发送消息过于频繁时会收到友好的提示，有效防止刷屏行为。
