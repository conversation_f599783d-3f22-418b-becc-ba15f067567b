name: codecov

on: [push, pull_request]

jobs:
  tests:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.0
          extensions: pdo, pdo_mysql, mbstring #optional, setup extensions
          coverage: xdebug #optional, setup coverage driver

      - name: Check Version
        run: |
          php -v
          php -m
          composer -V

      - name: Validate composer.json and composer.lock
        run: composer validate

      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache vendor
        uses: actions/cache@v4
        env:
          cache-name: composer-cache
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-build-${{ env.cache-name }}

      - name: Install dependencies (composer.lock)
        run: composer install --prefer-dist --no-progress --no-suggest

      - name: Run test suite
        run: composer exec -- phpunit --coverage-clover=coverage.xml -v
        env:
          TESTS_DB_MYSQL_HOST: 127.0.0.1
          TESTS_DB_MYSQL_PORT: 3306
          TESTS_DB_MYSQL_USERNAME: root
          TESTS_DB_MYSQL_PASSWORD: password
          TESTS_DB_MYSQL_DATABASE: testing

      - name: Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }} #required
          file: ./coverage.xml #optional
          flags: unittests #optional
          name: codecov-umbrella #optional
