<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信公众号功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007cba;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h3 {
            color: #333;
            margin-top: 0;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px 10px 5px 0;
        }
        .test-link:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            border-left: 4px solid #007cba;
            margin: 10px 0;
        }
        .emoji-test {
            font-size: 24px;
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 微信公众号功能测试</h1>
        
        <div class="section">
            <h3>📱 微信验证接口测试</h3>
            <p>测试微信服务器验证功能：</p>
            <a href="/api/wechat/verify?signature=test&timestamp=1627296000&nonce=test&echostr=hello" 
               class="test-link" target="_blank">测试验证接口</a>
            <div class="code">
                GET /api/wechat/verify?signature=test&timestamp=1627296000&nonce=test&echostr=hello
            </div>
        </div>
        
        <div class="section">
            <h3>🖼️ 图片管理功能</h3>
            <p>查看和管理保存的图片：</p>
            <a href="/images/manage" class="test-link" target="_blank">图片管理页面</a>
            <a href="/api/images/stats" class="test-link" target="_blank">图片统计API</a>
            <a href="/api/images" class="test-link" target="_blank">图片列表API</a>
        </div>
        
        <div class="section">
            <h3>😊 表情符号测试</h3>
            <p>以下表情符号可以被系统识别：</p>
            <div class="emoji-test">
                😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇<br>
                🙂 🙃 😉 😌 😍 🥰 😘 😗 😙 😚<br>
                🤔 🤨 😐 😑 😶 🙄 😏 😣 😥 😮<br>
                🤐 😯 😪 😫 😴 😌 😛 😜 😝 🤤<br>
                🎉 🎊 🎈 🎁 🎀 🎂 🍰 🧁 🍭 🍬
            </div>
            <p><small>在微信中发送包含这些表情的消息，系统会自动识别并记录。</small></p>
        </div>
        
        <div class="section">
            <h3>📋 使用说明</h3>
            <ol>
                <li><strong>配置微信公众号</strong>：
                    <ul>
                        <li>服务器地址：<code>https://yourdomain.com/api/wechat/verify</code></li>
                        <li>Token：在配置文件中设置</li>
                        <li>消息加解密：选择明文模式</li>
                    </ul>
                </li>
                <li><strong>发送消息测试</strong>：
                    <ul>
                        <li>发送文本消息（包含表情）</li>
                        <li>发送图片消息</li>
                        <li>系统会自动保存并回复</li>
                    </ul>
                </li>
                <li><strong>查看结果</strong>：
                    <ul>
                        <li>访问图片管理页面查看保存的图片</li>
                        <li>检查日志文件查看处理记录</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div class="section">
            <h3>📁 目录结构</h3>
            <div class="code">
public/upload/
├── normal/     # 普通图片
│   └── 2024/07/26/
└── emoji/      # 表情图片
    └── 2024/07/26/

runtime/log/
├── emoji_usage.log    # 表情使用记录
└── image_saves.log    # 图片保存记录
            </div>
        </div>
        
        <div class="section">
            <h3>🔧 API接口</h3>
            <ul>
                <li><code>GET/POST /api/wechat/verify</code> - 微信验证接口</li>
                <li><code>GET /api/images</code> - 获取图片列表</li>
                <li><code>GET /api/images/stats</code> - 获取统计信息</li>
                <li><code>GET /images/manage</code> - 图片管理页面</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>确保 <code>public/upload/</code> 目录有写入权限</li>
                <li>检查PHP的curl扩展是否启用</li>
                <li>生产环境建议使用HTTPS</li>
                <li>定期清理过期的图片文件</li>
                <li>监控磁盘空间使用情况</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的状态检查
        document.addEventListener('DOMContentLoaded', function() {
            // 检查上传目录是否存在
            fetch('/upload/')
                .then(response => {
                    if (response.ok) {
                        console.log('Upload directory is accessible');
                    }
                })
                .catch(error => {
                    console.log('Upload directory check failed:', error);
                });
        });
    </script>
</body>
</html>
