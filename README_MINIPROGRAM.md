# 微信公众号小程序回复功能

## 功能概述

当用户向微信公众号发送"收到不支持的消息类型，暂无法显示"这个特定内容时，系统会自动回复一个小程序卡片，引导用户进入小程序查看专属表情包。

## 🎯 功能特性

### 1. **智能内容识别**
- ✅ 精确匹配特定文本内容
- ✅ 自动触发小程序回复
- ✅ 详细的日志记录

### 2. **小程序卡片回复**
- ✅ 消息类型：`miniprogrampage`
- ✅ 小程序AppID：`wxa484f0497f834791`
- ✅ 页面路径：`pages/gif/gif`
- ✅ 动态参数：`openid=用户openid`

### 3. **用户体验优化**
- ✅ 个性化参数传递
- ✅ 友好的卡片标题
- ✅ 无缝跳转体验

## 📋 触发条件

### 触发文本
```
收到不支持的消息类型，暂无法显示
```

### 触发流程
1. 用户发送特定文本内容
2. 系统检测到匹配内容
3. 提取用户openid
4. 构建小程序页面路径（带参数）
5. 返回小程序卡片消息

## 🔧 技术实现

### 核心代码位置
- **文件**: `app/api/controller/Wechat.php`
- **方法**: `handleTextMessage()` 和 `replyMiniProgramMessage()`

### 消息处理逻辑
```php
// 在 handleTextMessage 方法中
if ($content === '收到不支持的消息类型，暂无法显示') {
    return $this->replyMiniProgramMessage($message, $fromUser);
}
```

### 小程序回复格式
```xml
<xml>
<ToUserName><![CDATA[用户openid]]></ToUserName>
<FromUserName><![CDATA[公众号原始ID]]></FromUserName>
<CreateTime>时间戳</CreateTime>
<MsgType><![CDATA[miniprogrampage]]></MsgType>
<Title><![CDATA[点击查看您的专属表情包]]></Title>
<AppId><![CDATA[wxa484f0497f834791]]></AppId>
<PagePath><![CDATA[pages/gif/gif?openid=用户openid]]></PagePath>
<ThumbMediaId><![CDATA[]]></ThumbMediaId>
</xml>
```

## 📊 配置参数

### 小程序信息
| 参数 | 值 | 说明 |
|------|----|----|
| AppID | wxa484f0497f834791 | 小程序的唯一标识 |
| 页面路径 | pages/gif/gif | 小程序内的页面路径 |
| 参数 | openid=用户openid | 传递给小程序的参数 |
| 卡片标题 | 点击查看您的专属表情包 | 显示在聊天界面的标题 |

### 可自定义配置
如需修改配置，可在 `replyMiniProgramMessage` 方法中调整：

```php
// 小程序配置
$appId = 'wxa484f0497f834791';           // 小程序AppID
$pagePath = 'pages/gif/gif';             // 页面路径
$title = '点击查看您的专属表情包';        // 卡片标题
```

## 🧪 测试方法

### 1. 可视化测试页面
访问：`/test/miniprogram`

测试功能包括：
- 发送触发消息测试
- 发送普通消息对比
- 查看XML响应格式
- 实时日志监控

### 2. 手动测试步骤

#### 使用微信开发者工具测试
1. 在微信公众平台配置服务器URL
2. 使用微信开发者工具发送测试消息
3. 发送内容：`收到不支持的消息类型，暂无法显示`
4. 观察是否返回小程序卡片

#### 使用cURL测试
```bash
curl -X POST http://yourdomain.com/api/wechat/verify \
  -H "Content-Type: application/xml" \
  -d '<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[oft-jviMe0JPszvjOVASgnja8U0o]]></FromUserName>
<CreateTime>1627296000</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[收到不支持的消息类型，暂无法显示]]></Content>
<MsgId>123456</MsgId>
</xml>'
```

### 3. 预期响应
成功时应返回包含以下内容的XML：
- `MsgType`: `miniprogrampage`
- `AppId`: `wxa484f0497f834791`
- `PagePath`: `pages/gif/gif?openid=用户openid`

## 📝 日志记录

### 日志内容
系统会记录以下信息：
- 触发检测日志
- 小程序回复构建过程
- 参数传递详情

### 日志示例
```
[2024-07-26T23:30:00+08:00][info] 检测到不支持消息类型，返回小程序链接: oft-jviMe0JPszvjOVASgnja8U0o
[2024-07-26T23:30:00+08:00][info] 回复小程序消息: appId=wxa484f0497f834791, pagePath=pages/gif/gif?openid=oft-jviMe0JPszvjOVASgnja8U0o, fromUser=oft-jviMe0JPszvjOVASgnja8U0o
```

### 查看日志
```bash
# 查看实时日志
tail -f runtime/log/202507/26.log

# 过滤小程序相关日志
tail -f runtime/log/202507/26.log | grep "小程序\|miniprogram"
```

## 🔍 故障排除

### 常见问题

#### 1. 小程序卡片不显示
**可能原因**：
- 小程序AppID不正确
- 小程序未关联到公众号
- 页面路径不存在

**解决方法**：
- 检查小程序AppID是否正确
- 在微信公众平台关联小程序
- 确认小程序页面路径存在

#### 2. 参数传递失败
**可能原因**：
- openid格式不正确
- 页面路径参数构建错误

**解决方法**：
- 检查openid是否有效
- 验证页面路径参数格式

#### 3. 触发条件不生效
**可能原因**：
- 文本内容不完全匹配
- 编码问题导致字符不一致

**解决方法**：
- 确保文本内容完全一致
- 检查字符编码

### 调试方法

#### 1. 启用详细日志
在代码中添加更多日志输出：
```php
trace('接收到的内容: [' . $content . ']');
trace('内容长度: ' . strlen($content));
trace('是否匹配: ' . ($content === '收到不支持的消息类型，暂无法显示' ? 'true' : 'false'));
```

#### 2. 使用测试工具
- 访问 `/test/miniprogram` 进行可视化测试
- 使用微信开发者工具的消息调试功能
- 查看系统日志文件

## 🚀 扩展功能

### 1. 多条件触发
可以扩展支持多种触发条件：
```php
$triggerMessages = [
    '收到不支持的消息类型，暂无法显示',
    '查看表情包',
    '打开小程序'
];

if (in_array($content, $triggerMessages)) {
    return $this->replyMiniProgramMessage($message, $fromUser);
}
```

### 2. 动态小程序配置
支持根据用户或场景动态选择小程序：
```php
protected function getMiniProgramConfig($fromUser) {
    // 根据用户类型返回不同的小程序配置
    return [
        'appId' => 'wxa484f0497f834791',
        'pagePath' => 'pages/gif/gif',
        'title' => '点击查看您的专属表情包'
    ];
}
```

### 3. 缩略图支持
添加小程序卡片缩略图：
```php
// 在 replyMiniProgramMessage 方法中
$thumbMediaId = $this->getThumbMediaId($fromUser);
```

### 4. 统计分析
记录小程序卡片的点击和使用情况：
```php
protected function logMiniProgramUsage($fromUser, $appId, $pagePath) {
    // 记录使用统计
}
```

## 📱 小程序端配置

### 页面接收参数
在小程序的 `pages/gif/gif.js` 中接收参数：
```javascript
Page({
  onLoad: function(options) {
    const openid = options.openid;
    console.log('接收到openid:', openid);
    // 使用openid加载用户专属内容
  }
})
```

### 参数验证
建议在小程序端验证参数：
```javascript
onLoad: function(options) {
  if (!options.openid) {
    wx.showToast({
      title: '参数错误',
      icon: 'error'
    });
    return;
  }
  
  this.loadUserGif(options.openid);
}
```

现在你的微信公众号已经具备了智能小程序回复功能！当用户发送特定内容时，会自动收到个性化的小程序卡片，引导用户查看专属表情包。
