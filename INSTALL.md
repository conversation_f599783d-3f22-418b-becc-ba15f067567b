# ThinkPHP6 微信公众号项目安装指南

## 环境要求

- PHP >= 7.2.5
- Composer
- MySQL >= 5.7 或 MariaDB >= 10.2
- 支持 URL 重写的 Web 服务器（Apache/Nginx）

## 安装步骤

### 1. 安装依赖

使用 Composer 安装项目依赖：

```bash
composer install
```

如果遇到网络问题，可以使用国内镜像：

```bash
composer config repo.packagist composer https://mirrors.aliyun.com/composer/
composer install
```

### 2. 环境配置

复制环境配置文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和微信相关信息：

```env
# 应用配置
APP_DEBUG = true

# 数据库配置
DATABASE_TYPE = mysql
DATABASE_HOSTNAME = 127.0.0.1
DATABASE_DATABASE = your_database_name
DATABASE_USERNAME = your_username
DATABASE_PASSWORD = your_password
DATABASE_HOSTPORT = 3306

# 微信公众号配置
WECHAT_TOKEN = your_wechat_token_here
WECHAT_APP_ID = your_app_id_here
WECHAT_APP_SECRET = your_app_secret_here
```

### 3. 目录权限

确保以下目录可写：

```bash
chmod -R 755 runtime/
chmod -R 755 public/
```

### 4. Web服务器配置

#### Apache 配置

确保启用了 `mod_rewrite` 模块，项目根目录的 `.htaccess` 文件内容：

```apache
<IfModule mod_rewrite.c>
  Options +FollowSymlinks -Multiviews
  RewriteEngine On

  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteRule ^(.*)$ index.php/$1 [QSA,PT,L]
</IfModule>
```

#### Nginx 配置

在 Nginx 配置文件中添加：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/project/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 5. 测试安装

访问你的域名，如果看到 ThinkPHP 的欢迎页面，说明安装成功。

测试微信验证接口：

```bash
curl "http://your-domain.com/api/wechat/verify?signature=test&timestamp=123456&nonce=test&echostr=hello"
```

### 6. 微信公众平台配置

1. 登录微信公众平台
2. 进入"开发" -> "基本配置"
3. 设置服务器配置：
   - URL: `https://your-domain.com/api/wechat/verify`
   - Token: 与 `.env` 中的 `WECHAT_TOKEN` 一致
   - 消息加解密方式: 明文模式

## 常见问题

### 1. Composer 安装失败

```bash
# 清除缓存
composer clear-cache

# 使用国内镜像
composer config repo.packagist composer https://mirrors.aliyun.com/composer/
```

### 2. 权限问题

```bash
# 设置正确的文件权限
sudo chown -R www-data:www-data /path/to/project
chmod -R 755 runtime/
```

### 3. 微信验证失败

- 检查 Token 是否一致
- 确认 URL 可以公网访问
- 检查服务器时间是否正确
- 查看日志文件 `runtime/log/`

### 4. 路由不生效

- 检查 Web 服务器是否支持 URL 重写
- 确认 `.htaccess` 或 Nginx 配置正确

## 开发模式

开发时可以启用调试模式：

```env
APP_DEBUG = true
APP_TRACE = true
```

## 生产环境部署

1. 关闭调试模式：
   ```env
   APP_DEBUG = false
   ```

2. 优化自动加载：
   ```bash
   composer install --optimize-autoloader --no-dev
   ```

3. 配置 HTTPS（微信要求）

4. 设置适当的文件权限

5. 配置日志轮转

## 更新依赖

```bash
composer update
```

## 技术支持

如果遇到问题，可以：

1. 查看 ThinkPHP6 官方文档
2. 检查项目日志文件
3. 查看微信公众平台开发文档
