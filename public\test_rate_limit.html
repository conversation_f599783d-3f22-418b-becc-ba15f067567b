<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>频率限制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007cba;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .btn {
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
            color: #856404;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .config-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .countdown {
            font-weight: bold;
            color: #dc3545;
        }
        .test-log {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⏱️ 频率限制测试</h1>
        
        <div class="config-info">
            <h3>📋 功能说明</h3>
            <p>系统会限制用户发送消息的频率，防止刷屏行为。</p>
            <ul>
                <li><strong>限制规则</strong>：同一用户3秒内只能发送一条消息</li>
                <li><strong>限制消息</strong>：您手速过快，请3s发送一次</li>
                <li><strong>适用范围</strong>：文本消息、图片消息</li>
                <li><strong>缓存时间</strong>：10秒（足够用于频率控制）</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>🧪 频率限制测试</h3>
            <label>测试用户OpenID:</label>
            <input type="text" id="test-openid" value="test_user_rate_limit" placeholder="输入测试用户的openid">
            
            <div style="margin: 15px 0;">
                <button class="btn" id="send-btn" onclick="sendMessage()">发送消息</button>
                <button class="btn" onclick="sendRapidMessages()">快速连发测试</button>
                <button class="btn" onclick="clearResults()">清空结果</button>
            </div>
            
            <div id="countdown-display" style="margin: 10px 0;"></div>
            <div id="test-result"></div>
        </div>
        
        <div class="section">
            <h3>📊 批量测试</h3>
            <p>测试连续发送多条消息的频率限制效果：</p>
            <button class="btn" onclick="batchTest(5)">发送5条消息</button>
            <button class="btn" onclick="batchTest(10)">发送10条消息</button>
            <button class="btn" onclick="testWithDelay()">间隔发送测试</button>
            <div id="batch-result"></div>
        </div>
        
        <div class="section">
            <h3>📝 测试日志</h3>
            <div id="test-log" class="test-log"></div>
        </div>
        
        <div class="section">
            <h3>🔍 技术实现</h3>
            <h4>频率检查逻辑:</h4>
            <pre>protected function isUserSendingTooFast(string $fromUser): bool
{
    $cacheKey = 'user_last_send_time_' . md5($fromUser);
    $lastSendTime = cache($cacheKey);
    
    if ($lastSendTime && (time() - $lastSendTime) < 3) {
        return true; // 3秒内重复发送
    }
    
    return false;
}</pre>
            
            <h4>时间记录:</h4>
            <pre>protected function recordUserLastSendTime(string $fromUser): void
{
    $cacheKey = 'user_last_send_time_' . md5($fromUser);
    cache($cacheKey, time(), 10); // 缓存10秒
}</pre>
        </div>
    </div>
    
    <script>
        let testCount = 0;
        let lastSendTime = 0;
        
        function addLog(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function sendMessage() {
            const openid = document.getElementById('test-openid').value.trim();
            const resultDiv = document.getElementById('test-result');
            const sendBtn = document.getElementById('send-btn');
            
            if (!openid) {
                resultDiv.innerHTML = '<div class="result error">请输入用户OpenID</div>';
                return;
            }
            
            const currentTime = Date.now();
            const timeSinceLastSend = (currentTime - lastSendTime) / 1000;
            
            addLog(`发送消息，距离上次发送: ${timeSinceLastSend.toFixed(1)}秒`);
            
            testCount++;
            const testXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[${openid}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[测试消息 #${testCount}]]></Content>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            // 禁用按钮并显示倒计时
            sendBtn.disabled = true;
            startCountdown();
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: testXml
                });
                
                const responseText = await response.text();
                const isRateLimit = responseText.includes('您手速过快，请3s发送一次');
                
                resultDiv.innerHTML = `
                    <div class="result ${isRateLimit ? 'warning' : 'success'}">
                        <h4>${isRateLimit ? '⚠️ 触发频率限制' : '✅ 消息发送成功'}</h4>
                        <p><strong>发送间隔:</strong> ${timeSinceLastSend.toFixed(1)}秒</p>
                        <p><strong>响应内容:</strong></p>
                        <pre>${responseText}</pre>
                    </div>
                `;
                
                addLog(`响应: ${isRateLimit ? '频率限制' : '正常回复'}`);
                lastSendTime = currentTime;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                addLog(`请求失败: ${error.message}`);
            }
        }
        
        function startCountdown() {
            const countdownDiv = document.getElementById('countdown-display');
            const sendBtn = document.getElementById('send-btn');
            let seconds = 3;
            
            const updateCountdown = () => {
                if (seconds > 0) {
                    countdownDiv.innerHTML = `<span class="countdown">请等待 ${seconds} 秒后再发送</span>`;
                    seconds--;
                    setTimeout(updateCountdown, 1000);
                } else {
                    countdownDiv.innerHTML = '';
                    sendBtn.disabled = false;
                }
            };
            
            updateCountdown();
        }
        
        async function sendRapidMessages() {
            addLog('开始快速连发测试...');
            const openid = document.getElementById('test-openid').value.trim();
            
            for (let i = 0; i < 3; i++) {
                addLog(`发送第 ${i + 1} 条消息`);
                await sendSingleMessage(openid, `快速消息 #${i + 1}`);
                await sleep(100); // 间隔100毫秒
            }
        }
        
        async function batchTest(count) {
            const resultDiv = document.getElementById('batch-result');
            const openid = document.getElementById('test-openid').value.trim();
            
            addLog(`开始批量测试，发送 ${count} 条消息`);
            resultDiv.innerHTML = '<div class="result">正在批量测试...</div>';
            
            let successCount = 0;
            let rateLimitCount = 0;
            
            for (let i = 0; i < count; i++) {
                const result = await sendSingleMessage(openid, `批量消息 #${i + 1}`);
                if (result.isRateLimit) {
                    rateLimitCount++;
                } else {
                    successCount++;
                }
                await sleep(200); // 间隔200毫秒
            }
            
            resultDiv.innerHTML = `
                <div class="result success">
                    <h4>📊 批量测试完成</h4>
                    <p><strong>总发送:</strong> ${count} 条</p>
                    <p><strong>成功发送:</strong> ${successCount} 条</p>
                    <p><strong>频率限制:</strong> ${rateLimitCount} 条</p>
                </div>
            `;
            
            addLog(`批量测试完成: 成功${successCount}条, 限制${rateLimitCount}条`);
        }
        
        async function testWithDelay() {
            addLog('开始间隔发送测试...');
            const openid = document.getElementById('test-openid').value.trim();
            
            for (let i = 0; i < 3; i++) {
                addLog(`发送第 ${i + 1} 条消息，等待4秒间隔`);
                await sendSingleMessage(openid, `间隔消息 #${i + 1}`);
                if (i < 2) { // 最后一条不需要等待
                    await sleep(4000); // 等待4秒
                }
            }
        }
        
        async function sendSingleMessage(openid, content) {
            const testXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[${openid}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[${content}]]></Content>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: testXml
                });
                
                const responseText = await response.text();
                const isRateLimit = responseText.includes('您手速过快，请3s发送一次');
                
                return { isRateLimit, responseText };
            } catch (error) {
                addLog(`发送失败: ${error.message}`);
                return { isRateLimit: false, responseText: 'ERROR' };
            }
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        function clearResults() {
            document.getElementById('test-result').innerHTML = '';
            document.getElementById('batch-result').innerHTML = '';
            document.getElementById('test-log').innerHTML = '';
            testCount = 0;
            lastSendTime = 0;
        }
        
        // 页面加载时添加初始日志
        document.addEventListener('DOMContentLoaded', function() {
            addLog('频率限制测试页面已加载');
        });
    </script>
</body>
</html>
