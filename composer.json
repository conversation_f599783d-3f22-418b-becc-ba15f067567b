{"name": "your-project/wechat-api", "description": "ThinkPHP6 微信公众号API项目", "type": "project", "keywords": ["thinkphp", "wechat", "api", "php"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "your-name", "email": "<EMAIL>"}], "require": {"php": ">=7.2.5", "topthink/framework": "^6.0.0", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-curl": "*", "ext-simplexml": "*"}, "require-dev": {"symfony/var-dumper": "^4.2"}, "autoload": {"psr-4": {"app\\": "app/"}, "psr-0": {"": "extend/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "config": {"preferred-install": "dist", "optimize-autoloader": true, "sort-packages": true}, "scripts": {"post-autoload-dump": []}, "extra": {"think": {"services": []}}, "minimum-stability": "dev", "prefer-stable": true}