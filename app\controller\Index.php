<?php
declare(strict_types=1);

namespace app\controller;

use app\BaseController;

class Index extends BaseController
{
    public function index()
    {
        return 'Hello ThinkPHP6!';
    }

    public function test()
    {
        return json([
            'code' => 200,
            'message' => 'API is working',
            'data' => [
                'time' => date('Y-m-d H:i:s'),
                'version' => 'ThinkPHP 6.0'
            ]
        ]);
    }
}
