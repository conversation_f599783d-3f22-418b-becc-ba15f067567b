<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\service\ImageService;

/**
 * 微信公众号控制器 - 简化版本
 */
class Wechat
{
    /**
     * 微信服务器验证接口
     */
    public function verify()
    {
        // 获取请求参数
        $signature = input('signature', '');
        $timestamp = input('timestamp', '');
        $nonce = input('nonce', '');
        $echostr = input('echostr', '');
        
        // 微信Token（从配置获取）
        $token = config('wechat.token', 'your_wechat_token_here');
        
        // 记录日志
        trace('微信验证请求: ' . json_encode([
            'method' => request()->method(),
            'signature' => $signature,
            'timestamp' => $timestamp,
            'nonce' => $nonce,
            'echostr' => $echostr,
            'ip' => request()->ip()
        ]));
        
        // GET请求 - 验证服务器地址
        if (request()->isGet()) {
            return $this->handleVerification($signature, $timestamp, $nonce, $echostr, $token);
        }
        
        // POST请求 - 处理微信消息推送
        if (request()->isPost()) {
            return $this->handleMessage($signature, $timestamp, $nonce, $token);
        }
        
        return 'Method not allowed';
    }
    
    /**
     * 处理服务器验证
     */
    protected function handleVerification($signature, $timestamp, $nonce, $echostr, $token)
    {
        if (empty($signature) || empty($timestamp) || empty($nonce) || empty($echostr)) {
            trace('微信验证失败: 缺少必要参数');
            return 'Missing parameters';
        }
        
        // 验证签名
        if ($this->checkSignature($signature, $timestamp, $nonce, $token)) {
            trace('微信验证成功: ' . $echostr);
            return $echostr;
        } else {
            trace('微信验证失败: 签名不匹配');
            return 'Signature verification failed';
        }
    }
    
    /**
     * 处理微信消息推送
     */
    protected function handleMessage($signature, $timestamp, $nonce, $token)
    {
        // 验证签名
        if (!$this->checkSignature($signature, $timestamp, $nonce, $token)) {
            trace('消息推送签名验证失败');
            return 'Signature verification failed';
        }

        // 获取POST数据
        $postData = file_get_contents('php://input');
        trace('接收到微信消息原始数据: ' . $postData);

        // 解析XML消息
        $message = $this->parseMessage($postData);

        if ($message) {
            trace('消息解析成功', $message);
            // 处理不同类型的消息
            $response = $this->processMessage($message);
            trace('消息处理完成，响应: ' . $response);
            return $response;
        } else {
            trace('消息解析失败，返回success');
        }

        // 返回success表示接收成功
        return 'success';
    }
    
    /**
     * 解析微信消息XML
     */
    protected function parseMessage($xmlData)
    {
        if (empty($xmlData)) {
            return false;
        }

        try {
            $xml = simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml === false) {
                trace('XML解析失败');
                return false;
            }

            return [
                'ToUserName' => (string)$xml->ToUserName,
                'FromUserName' => (string)$xml->FromUserName,
                'CreateTime' => (string)$xml->CreateTime,
                'MsgType' => (string)$xml->MsgType,
                'MsgId' => (string)($xml->MsgId ?? ''),
                'Content' => (string)($xml->Content ?? ''),
                'MediaId' => (string)($xml->MediaId ?? ''),
                'PicUrl' => (string)($xml->PicUrl ?? ''),
                'Format' => (string)($xml->Format ?? ''),
                'Recognition' => (string)($xml->Recognition ?? ''),
                'ThumbMediaId' => (string)($xml->ThumbMediaId ?? ''),
                'Location_X' => (string)($xml->Location_X ?? ''),
                'Location_Y' => (string)($xml->Location_Y ?? ''),
                'Scale' => (string)($xml->Scale ?? ''),
                'Label' => (string)($xml->Label ?? ''),
                'Title' => (string)($xml->Title ?? ''),
                'Description' => (string)($xml->Description ?? ''),
                'Url' => (string)($xml->Url ?? ''),
                'Event' => (string)($xml->Event ?? ''),
                'EventKey' => (string)($xml->EventKey ?? ''),
            ];
        } catch (Exception $e) {
            trace('消息解析异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理微信消息
     */
    protected function processMessage($message)
    {
        $msgType = $message['MsgType'];

        trace('处理消息类型: ' . $msgType, $message);

        switch ($msgType) {
            case 'text':
                return $this->handleTextMessage($message);
            case 'image':
                return $this->handleImageMessage($message);
            case 'voice':
                return $this->handleVoiceMessage($message);
            case 'video':
                return $this->handleVideoMessage($message);
            case 'event':
                return $this->handleEventMessage($message);
            default:
                return $this->replyTextMessage($message, '暂不支持此类型消息');
        }
    }

    /**
     * 处理文本消息
     */
    protected function handleTextMessage($message)
    {
        $content = trim($message['Content']);
        $fromUser = $message['FromUserName'];

        // 检查是否是"收到不支持的消息类型，暂无法显示"
        if ($content === '收到不支持的消息类型，暂无法显示') {
            trace('检测到不支持消息类型，返回小程序链接: ' . $fromUser);
            return $this->replyMiniProgramMessage($message, $fromUser);
        }

        // 检查是否包含表情符号
        if ($this->containsEmoji($content)) {
            // 如果是纯表情或包含表情的文本
            $response = "检测到表情符号！\n";
            $response .= "内容: " . $content . "\n";
            $response .= "表情已记录 😊";

            // 记录表情使用
            $this->logEmojiUsage($message['FromUserName'], $content);

            return $this->replyTextMessage($message, $response);
        }

        // 普通文本消息的处理
        return $this->replyTextMessage($message, "收到您的消息: " . $content);
    }

    /**
     * 处理图片消息（包括表情图片）
     */
    protected function handleImageMessage($message)
    {
        $picUrl = $message['PicUrl'];
        $mediaId = $message['MediaId'];
        $fromUser = $message['FromUserName'];

        trace('收到图片消息', [
            'from' => $fromUser,
            'picUrl' => $picUrl,
            'mediaId' => $mediaId
        ]);

        // 检查图片URL是否为空
        if (empty($picUrl)) {
            trace('图片URL为空，无法下载');
            return $this->replyTextMessage($message, "抱歉，无法获取图片地址，请重新发送。");
        }

        // 判断是否为表情图片（这里可以根据实际需求调整判断逻辑）
        $imageType = $this->detectImageType($picUrl, $mediaId);
        trace('图片类型检测结果: ' . $imageType);

        // 使用ImageService下载并保存图片
        $imageService = new ImageService();
        trace('开始下载图片: ' . $picUrl);
        $savedInfo = $imageService->downloadWechatImage($picUrl, $fromUser, $imageType);
        trace('图片下载结果', $savedInfo);

        if ($savedInfo['success']) {
            $typeText = $imageType === 'emoji' ? '表情图片' : '图片';
            $response = "{$typeText}已保存成功！\n";
            $response .= "文件名: " . $savedInfo['filename'] . "\n";
            $response .= "访问地址: " . $savedInfo['url'] . "\n";
            $response .= "文件大小: " . $this->formatFileSize($savedInfo['size']) . "\n";
            $response .= "保存时间: " . date('Y-m-d H:i:s');

            // 如果是表情，添加特殊提示
            if ($imageType === 'emoji') {
                $response .= "\n\n😊 检测到表情图片，已保存到表情专用目录！";
            }

            return $this->replyTextMessage($message, $response);
        } else {
            return $this->replyTextMessage($message, "图片保存失败: " . $savedInfo['error']);
        }
    }

    /**
     * 验证微信服务器签名
     */
    protected function checkSignature($signature, $timestamp, $nonce, $token)
    {
        // 将token、timestamp、nonce三个参数进行字典序排序
        $tmpArr = [$token, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);

        // 将三个参数字符串拼接成一个字符串进行sha1加密
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);

        // 开发者获得加密后的字符串可与signature对比
        return $tmpStr === $signature;
    }

    /**
     * 检查文本是否包含表情符号
     */
    protected function containsEmoji($text)
    {
        // 使用正则表达式检测Unicode表情符号
        $emojiPattern = '/[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]/u';
        return preg_match($emojiPattern, $text);
    }

    /**
     * 记录表情使用情况
     */
    protected function logEmojiUsage($fromUser, $content)
    {
        $logData = [
            'user' => $fromUser,
            'content' => $content,
            'time' => date('Y-m-d H:i:s'),
            'type' => 'emoji'
        ];

        trace('表情使用记录', $logData);

        // 这里可以保存到数据库
        // 暂时写入日志文件
        $logFile = root_path() . 'runtime/log/emoji_usage.log';
        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
    }

    /**
     * 下载并保存图片
     */
    protected function downloadAndSaveImage($picUrl, $fromUser)
    {
        try {
            // 创建upload目录
            $uploadDir = root_path() . 'public/upload/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // 按日期创建子目录
            $dateDir = $uploadDir . date('Y/m/d') . '/';
            if (!is_dir($dateDir)) {
                mkdir($dateDir, 0755, true);
            }

            // 生成文件名
            $extension = $this->getImageExtension($picUrl);
            $filename = 'wechat_' . $fromUser . '_' . time() . '_' . uniqid() . '.' . $extension;
            $filepath = $dateDir . $filename;

            // 下载图片
            $imageData = $this->downloadImage($picUrl);
            if ($imageData === false) {
                return ['success' => false, 'error' => '图片下载失败'];
            }

            // 保存图片
            if (file_put_contents($filepath, $imageData) === false) {
                return ['success' => false, 'error' => '图片保存失败'];
            }

            // 生成访问URL
            $serverUrl = $this->getServerUrl();
            $relativeUrl = 'upload/' . date('Y/m/d') . '/' . $filename;
            $fullUrl = $serverUrl . '/' . $relativeUrl;

            // 记录保存信息
            $saveInfo = [
                'user' => $fromUser,
                'original_url' => $picUrl,
                'saved_path' => $filepath,
                'access_url' => $fullUrl,
                'filename' => $filename,
                'size' => strlen($imageData),
                'time' => date('Y-m-d H:i:s')
            ];

            trace('图片保存成功', $saveInfo);

            // 记录到日志文件
            $logFile = root_path() . 'runtime/log/image_saves.log';
            file_put_contents($logFile, json_encode($saveInfo) . "\n", FILE_APPEND | LOCK_EX);

            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'url' => $fullUrl,
                'size' => strlen($imageData)
            ];

        } catch (Exception $e) {
            trace('图片保存异常: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 下载图片
     */
    protected function downloadImage($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; WechatBot/1.0)');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200 || $data === false) {
            return false;
        }

        return $data;
    }

    /**
     * 获取图片扩展名
     */
    protected function getImageExtension($url)
    {
        $pathInfo = pathinfo(parse_url($url, PHP_URL_PATH));
        $extension = $pathInfo['extension'] ?? 'jpg';

        // 确保是有效的图片扩展名
        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array(strtolower($extension), $validExtensions)) {
            $extension = 'jpg';
        }

        return $extension;
    }

    /**
     * 获取服务器URL
     */
    protected function getServerUrl()
    {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }

    /**
     * 处理语音消息
     */
    protected function handleVoiceMessage($message)
    {
        return $this->replyTextMessage($message, "收到您的语音消息，语音识别功能开发中...");
    }

    /**
     * 处理视频消息
     */
    protected function handleVideoMessage($message)
    {
        return $this->replyTextMessage($message, "收到您的视频消息，视频处理功能开发中...");
    }

    /**
     * 处理事件消息
     */
    protected function handleEventMessage($message)
    {
        $event = $message['Event'];

        switch ($event) {
            case 'subscribe':
                return $this->replyTextMessage($message, "欢迎关注！发送图片或表情，我会帮您保存到服务器哦~ 😊");
            case 'unsubscribe':
                trace('用户取消关注: ' . $message['FromUserName']);
                return 'success';
            default:
                return 'success';
        }
    }

    /**
     * 检测图片类型（表情或普通图片）
     */
    protected function detectImageType($picUrl, $mediaId)
    {
        // 这里可以根据实际需求实现更复杂的检测逻辑
        // 目前简单地根据URL特征或其他信息判断

        // 示例：如果URL包含特定关键词，认为是表情
        $emojiKeywords = ['emoji', 'sticker', 'expression'];
        foreach ($emojiKeywords as $keyword) {
            if (strpos(strtolower($picUrl), $keyword) !== false) {
                return 'emoji';
            }
        }

        // 也可以根据MediaId的特征判断
        // 微信表情包的MediaId可能有特定的格式

        // 默认返回普通图片
        return 'normal';
    }

    /**
     * 格式化文件大小
     */
    protected function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 回复文本消息
     */
    protected function replyTextMessage($message, $content)
    {
        $template = "<xml>
<ToUserName><![CDATA[%s]]></ToUserName>
<FromUserName><![CDATA[%s]]></FromUserName>
<CreateTime>%s</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[%s]]></Content>
</xml>";

        return sprintf(
            $template,
            $message['FromUserName'],
            $message['ToUserName'],
            time(),
            $content
        );
    }

    /**
     * 回复小程序消息
     */
    protected function replyMiniProgramMessage($message, $fromUser)
    {
        // 小程序配置
        $appId = 'wxa484f0497f834791';
        $pagePath = 'pages/gif/gif';
        $openid = $fromUser; // 使用发送消息的用户openid

        // 构建小程序页面路径（带参数）
        $pagePathWithParams = $pagePath . '?openid=' . $openid;

        // 记录小程序回复日志
        trace('回复小程序消息: appId=' . $appId . ', pagePath=' . $pagePathWithParams . ', fromUser=' . $fromUser);

        $template = "<xml>
<ToUserName><![CDATA[%s]]></ToUserName>
<FromUserName><![CDATA[%s]]></FromUserName>
<CreateTime>%s</CreateTime>
<MsgType><![CDATA[miniprogrampage]]></MsgType>
<Title><![CDATA[%s]]></Title>
<AppId><![CDATA[%s]]></AppId>
<PagePath><![CDATA[%s]]></PagePath>
<ThumbMediaId><![CDATA[%s]]></ThumbMediaId>
</xml>";

        return sprintf(
            $template,
            $message['FromUserName'],
            $message['ToUserName'],
            time(),
            '点击查看您的专属表情包', // 小程序卡片标题
            $appId,
            $pagePathWithParams,
            '' // ThumbMediaId 可以为空，或者设置一个默认的缩略图媒体ID
        );
    }
}
