# ThinkPHP6 微信公众号Token验证API

## 项目结构

```
├── app/
│   ├── BaseController.php              # 基础控制器
│   ├── api/
│   │   └── controller/
│   │       └── WechatController.php    # 微信控制器
│   └── common/
│       └── service/
│           └── WechatService.php       # 微信服务类
├── config/
│   └── wechat.php                      # 微信配置文件
├── route/
│   └── api.php                         # API路由配置
├── .env.example                        # 环境配置示例
└── README_WECHAT.md                    # 使用说明
```

## 配置步骤

### 1. 环境配置

复制 `.env.example` 为 `.env` 并配置微信相关参数：

```bash
cp .env.example .env
```

在 `.env` 文件中设置：

```env
# 微信公众号Token（自定义，需与微信后台一致）
WECHAT_TOKEN = your_custom_token

# 微信公众号AppID（从微信公众平台获取）
WECHAT_APP_ID = your_app_id

# 微信公众号AppSecret（从微信公众平台获取）
WECHAT_APP_SECRET = your_app_secret
```

### 2. 微信公众平台配置

1. 登录微信公众平台 (https://mp.weixin.qq.com)
2. 进入"开发" -> "基本配置"
3. 设置服务器配置：
   - **服务器地址(URL)**: `https://yourdomain.com/api/wechat/verify`
   - **Token**: 与 `.env` 中的 `WECHAT_TOKEN` 保持一致
   - **消息加解密方式**: 建议选择"明文模式"（简单）或"安全模式"
   - **数据格式**: XML

### 3. API接口说明

#### 验证接口
- **URL**: `/api/wechat/verify`
- **方法**: GET/POST
- **说明**: 
  - GET请求：用于微信服务器验证URL有效性
  - POST请求：用于接收微信服务器推送的消息和事件

#### 请求参数
- `signature`: 微信加密签名
- `timestamp`: 时间戳
- `nonce`: 随机数
- `echostr`: 随机字符串（仅GET请求）

## 核心功能

### WechatService 服务类

提供以下核心方法：

1. **checkSignature()**: 验证微信服务器签名
2. **responseEchoStr()**: 响应微信验证请求
3. **validateServer()**: 完整的服务器验证流程
4. **log()**: 记录日志

### WechatController 控制器

提供以下功能：

1. **verify()**: 主验证接口
2. **handleVerification()**: 处理GET验证请求
3. **handleMessage()**: 处理POST消息推送

## 使用示例

### 验证流程

1. 微信服务器发送GET请求到你的验证URL
2. 系统验证signature、timestamp、nonce参数
3. 验证通过后返回echostr参数
4. 微信服务器确认验证成功

### 消息接收

1. 用户在微信公众号发送消息
2. 微信服务器POST消息到你的URL
3. 系统验证签名后接收消息内容
4. 可以解析XML消息并进行相应处理

## 日志记录

系统会自动记录以下日志：
- 微信服务器请求日志
- 验证成功/失败日志
- 消息接收日志
- 异常错误日志

日志文件位置：`runtime/log/`

## 安全建议

1. **HTTPS**: 生产环境必须使用HTTPS
2. **Token安全**: Token应该足够复杂，不要使用简单字符串
3. **IP白名单**: 可以考虑限制只允许微信服务器IP访问
4. **日志监控**: 定期检查日志，监控异常请求

## 扩展功能

基于此基础，你可以继续开发：

1. **消息解析**: 解析XML格式的微信消息
2. **自动回复**: 根据消息类型自动回复
3. **菜单管理**: 创建和管理自定义菜单
4. **用户管理**: 获取用户信息和管理
5. **素材管理**: 上传和管理多媒体素材

## 测试

建议编写单元测试来验证功能：

```php
// 测试签名验证
$service = new WechatService();
$result = $service->checkSignature($signature, $timestamp, $nonce, $token);

// 测试完整验证流程
$result = $service->validateServer($params, $token);
```

## 故障排除

1. **验证失败**: 检查Token是否一致，时间戳是否正确
2. **无法访问**: 确认URL可以公网访问，支持GET/POST请求
3. **签名错误**: 检查参数排序和SHA1加密是否正确
4. **超时**: 微信要求5秒内响应，检查代码执行效率
