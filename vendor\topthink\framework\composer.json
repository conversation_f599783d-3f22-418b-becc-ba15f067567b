{"name": "topthink/framework", "description": "The ThinkPHP Framework.", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.2.5", "ext-json": "*", "ext-mbstring": "*", "psr/log": "~1.0", "psr/container": "~1.0", "psr/simple-cache": "^1.0", "psr/http-message": "^1.0", "topthink/think-orm": "^2.0|^3.0", "topthink/think-helper": "^3.1.1"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^7.0", "guzzlehttp/psr7": "^2.1.0"}, "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "autoload-dev": {"psr-4": {"think\\tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true}}