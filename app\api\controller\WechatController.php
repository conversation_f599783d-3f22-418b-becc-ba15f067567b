<?php
declare(strict_types=1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\WechatService;
use think\Request;
use think\Response;

/**
 * 微信公众号控制器
 */
class WechatController extends BaseController
{
    /**
     * 微信服务实例
     * @var WechatService
     */
    protected $wechatService;

    /**
     * 微信公众号Token（从配置文件获取）
     * @var string
     */
    protected $token;

    /**
     * 微信公众号AppID
     * @var string
     */
    protected $appId;

    /**
     * 微信公众号AppSecret
     * @var string
     */
    protected $appSecret;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->wechatService = new WechatService();
        $this->token = config('wechat.token', '');
        $this->appId = config('wechat.app_id', '');
        $this->appSecret = config('wechat.app_secret', '');

        if (empty($this->token)) {
            throw new \Exception('微信Token未配置');
        }
    }

    /**
     * 微信服务器验证接口
     * GET请求用于验证服务器地址的有效性
     * POST请求用于接收微信服务器推送的消息
     * 
     * @param Request $request
     * @return Response
     */
    public function verify(Request $request): Response
    {
        try {
            // 获取请求参数
            $params = $request->param();
            // 记录请求日志
            $this->wechatService->log('微信服务器请求', [
                'method' => $request->method(),
                'params' => $params,
                'ip' => $request->ip()
            ]);
            
            $this->wechatService->log(json_encode($request->getInput()));

            // GET请求 - 验证服务器地址
            if ($request->isGet()) {
                return $this->handleVerification($params);
            }
            
            // POST请求 - 处理微信消息推送
            if ($request->isPost()) {
                return $this->handleMessage($request, $params);
            }

            return $this->error('不支持的请求方法');

        } catch (\Exception $e) {
            $this->wechatService->log('微信验证异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return $this->error('服务器内部错误');
        }
    }

    /**
     * 处理服务器验证
     * 
     * @param array $params
     * @return Response
     */
    protected function handleVerification(array $params): Response
    {
        $result = $this->wechatService->validateServer($params, $this->token);
        
        if (!$result['status']) {
            $this->wechatService->log('微信验证失败', [
                'message' => $result['message'],
                'params' => $params
            ]);
            
            return response('验证失败', 403);
        }

        $this->wechatService->log('微信验证成功', [
            'echostr' => $result['data']
        ]);

        // 返回echostr给微信服务器
        return response($result['data']);
    }

    /**
     * 处理微信消息推送
     * 
     * @param Request $request
     * @param array $params
     * @return Response
     */
    protected function handleMessage(Request $request, array $params): Response
    {
        // 验证签名
        $signature = $params['signature'] ?? '';
        $timestamp = $params['timestamp'] ?? '';
        $nonce = $params['nonce'] ?? '';

        if (!$this->wechatService->checkSignature($signature, $timestamp, $nonce, $this->token)) {
            $this->wechatService->log('消息推送签名验证失败', $params);
            return response('签名验证失败', 403);
        }

        // 获取POST数据（XML格式的消息内容）
        $postData = $request->getInput();
        $this->wechatService->log(json_encode($postData));
        $this->wechatService->log(json_encode($params));
        $this->wechatService->log('接收到微信消息', [
            'params' => $params,
            'postData' => $postData
        ]);

        // 在处理消息时自动获取素材库信息
        // 可以通过URL参数控制获取数量，例如: /api/wechat/verify?material_count=5
        $materialCount = (int)$request->param('material_count', 10); // 默认获取10个
        $this->autoFetchMaterials($materialCount);

        // 这里可以解析XML消息并进行相应处理
        // 暂时返回success表示接收成功
        return response('success');
    }

    /**
     * 返回成功响应
     * 
     * @param mixed $data
     * @param string $message
     * @return Response
     */
    protected function success($data = null, string $message = 'success'): Response
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 返回错误响应
     * 
     * @param string $message
     * @param int $code
     * @return Response
     */
    protected function error(string $message = 'error', int $code = 400): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => null
        ], $code);
    }

    /**
     * 获取微信Access Token
     *
     * @return string|false
     */
    protected function getAccessToken()
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            $this->wechatService->log('获取AccessToken失败: AppID或AppSecret未配置');
            return false;
        }

        // 检查缓存中是否有有效的access_token
        $cacheKey = 'wechat_access_token_' . md5($this->appId);
        $cachedToken = cache($cacheKey);

        if ($cachedToken) {
            $this->wechatService->log('使用缓存的AccessToken: ' . substr($cachedToken, 0, 20) . '...');
            return $cachedToken;
        }

        // 请求新的access_token
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";

        $this->wechatService->log('请求AccessToken: ' . $url);

        $response = $this->httpGet($url);
        if (!$response) {
            $this->wechatService->log('获取AccessToken失败: HTTP请求失败');
            return false;
        }

        $data = json_decode($response, true);
        if (!$data || isset($data['errcode'])) {
            $this->wechatService->log('获取AccessToken失败: ' . json_encode($data));
            return false;
        }

        $accessToken = $data['access_token'];
        $expiresIn = $data['expires_in'] ?? 7200;

        // 缓存access_token，提前5分钟过期
        cache($cacheKey, $accessToken, $expiresIn - 300);

        $this->wechatService->log('获取AccessToken成功: ' . substr($accessToken, 0, 20) . '..., 有效期: ' . $expiresIn . '秒');

        return $accessToken;
    }

    /**
     * 判断是否应该获取素材库
     *
     * @return bool
     */
    protected function shouldFetchMaterials(): bool
    {
        // 使用缓存控制获取频率，每10分钟最多获取一次
        $cacheKey = 'wechat_material_fetch_lock';
        $lastFetchTime = cache($cacheKey);

        if ($lastFetchTime && (time() - $lastFetchTime) < 600) { // 10分钟 = 600秒
            return false;
        }

        // 设置缓存锁
        cache($cacheKey, time(), 600);

        return true;
    }

    /**
     * 自动获取素材库信息（在处理消息时调用）
     *
     * @param int $count 获取数量，默认10个
     * @return void
     */
    protected function autoFetchMaterials(int $count = 10): void
    {
        try {
            // 检查是否启用自动获取功能
            if (!config('wechat.auto_fetch_materials', true)) {
                $this->wechatService->log('自动获取素材库功能已禁用');
                return;
            }

            // 检查是否需要获取素材库（避免频繁调用）
            if (!$this->shouldFetchMaterials()) {
                $this->wechatService->log('跳过素材库获取（基于频率控制）');
                return;
            }

            $this->wechatService->log('开始自动获取image类型素材库信息', ['count' => $count]);

            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                $this->wechatService->log('自动获取素材库失败: AccessToken获取失败');
                return;
            }

            // 只获取image类型素材
            $type = 'image';
            $this->wechatService->log("正在获取{$type}类型素材，数量: {$count}");

            $result = $this->callBatchGetMaterialAPI($accessToken, $type, 0, $count);

            if ($result['success']) {
                $materialData = $result['data'];
                $itemCount = $materialData['item_count'] ?? 0;
                $totalMaterialCount = $materialData['total_count'] ?? 0;

                $this->wechatService->log("获取{$type}素材成功", [
                    'type' => $type,
                    'total_count' => $totalMaterialCount,
                    'item_count' => $itemCount,
                    'requested_count' => $count
                ]);

                // 记录详细素材信息
                if ($itemCount > 0) {
                    $this->logMaterialDetails($materialData, $type);
                }

                // 将汇总信息记录到专用日志文件
                $this->logToFile('auto_material_fetch.log', [
                    'trigger' => 'message_received',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'type' => $type,
                    'requested_count' => $count,
                    'actual_count' => $itemCount,
                    'total_count' => $totalMaterialCount,
                    'materials' => $materialData
                ]);

                $this->wechatService->log('自动获取素材库完成', [
                    'type' => $type,
                    'requested_count' => $count,
                    'actual_count' => $itemCount,
                    'total_count' => $totalMaterialCount
                ]);
            } else {
                $this->wechatService->log("获取{$type}素材失败: " . $result['message']);
            }

        } catch (\Exception $e) {
            $this->wechatService->log('自动获取素材库异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * 批量获取素材库
     *
     * @param Request $request
     * @return Response
     */
    public function batchGetMaterial(Request $request): Response
    {
        try {
            $type = $request->param('type', 'image'); // image, voice, video, thumb, news
            $offset = (int)$request->param('offset', 0);
            $count = (int)$request->param('count', 20);

            // 验证参数
            $allowedTypes = ['image', 'voice', 'video', 'thumb', 'news'];
            if (!in_array($type, $allowedTypes)) {
                return $this->error('不支持的素材类型，支持的类型: ' . implode(', ', $allowedTypes));
            }

            if ($count > 20 || $count < 1) {
                return $this->error('count参数必须在1-20之间');
            }

            $this->wechatService->log('开始获取素材库', [
                'type' => $type,
                'offset' => $offset,
                'count' => $count
            ]);

            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return $this->error('获取AccessToken失败');
            }

            // 调用微信API获取素材
            $result = $this->callBatchGetMaterialAPI($accessToken, $type, $offset, $count);

            if ($result['success']) {
                // 记录成功日志
                $this->wechatService->log('素材库获取成功', [
                    'type' => $type,
                    'total_count' => $result['data']['total_count'] ?? 0,
                    'item_count' => $result['data']['item_count'] ?? 0,
                    'offset' => $offset,
                    'count' => $count
                ]);

                // 详细记录素材信息到日志
                $this->logMaterialDetails($result['data'], $type);

                return $this->success($result['data'], '素材库获取成功');
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            $this->wechatService->log('获取素材库异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return $this->error('服务器内部错误: ' . $e->getMessage());
        }
    }

    /**
     * 调用微信批量获取素材API
     *
     * @param string $accessToken
     * @param string $type
     * @param int $offset
     * @param int $count
     * @return array
     */
    protected function callBatchGetMaterialAPI(string $accessToken, string $type, int $offset, int $count): array
    {
        $url = "https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token={$accessToken}";

        $postData = json_encode([
            'type' => $type,
            'offset' => $offset,
            'count' => $count
        ]);

        $this->wechatService->log('调用素材API', [
            'url' => $url,
            'post_data' => $postData
        ]);

        $response = $this->httpPost($url, $postData);

        if (!$response) {
            return [
                'success' => false,
                'message' => 'HTTP请求失败'
            ];
        }

        $data = json_decode($response, true);

        // 记录原始响应
        $this->wechatService->log('素材API响应', [
            'response_length' => strlen($response),
            'response_data' => $data
        ]);

        if (!$data) {
            return [
                'success' => false,
                'message' => 'JSON解析失败'
            ];
        }

        if (isset($data['errcode']) && $data['errcode'] != 0) {
            return [
                'success' => false,
                'message' => '微信API错误: ' . ($data['errmsg'] ?? '未知错误') . ' (错误码: ' . $data['errcode'] . ')'
            ];
        }

        return [
            'success' => true,
            'data' => $data
        ];
    }

    /**
     * 记录素材详细信息到日志
     *
     * @param array $materialData
     * @param string $type
     */
    protected function logMaterialDetails(array $materialData, string $type): void
    {
        if (!isset($materialData['item'])) {
            return;
        }

        $logData = [
            'type' => $type,
            'total_count' => $materialData['total_count'] ?? 0,
            'item_count' => $materialData['item_count'] ?? 0,
            'materials' => []
        ];

        foreach ($materialData['item'] as $index => $item) {
            $materialInfo = [
                'index' => $index,
                'media_id' => $item['media_id'] ?? '',
                'update_time' => $item['update_time'] ?? 0,
                'update_time_formatted' => isset($item['update_time']) ? date('Y-m-d H:i:s', $item['update_time']) : ''
            ];

            // 根据素材类型添加特定信息
            switch ($type) {
                case 'image':
                case 'thumb':
                    $materialInfo['name'] = $item['name'] ?? '';
                    $materialInfo['url'] = $item['url'] ?? '';
                    break;

                case 'voice':
                    $materialInfo['name'] = $item['name'] ?? '';
                    $materialInfo['url'] = $item['url'] ?? '';
                    break;

                case 'video':
                    $materialInfo['name'] = $item['name'] ?? '';
                    $materialInfo['introduction'] = $item['introduction'] ?? '';
                    $materialInfo['down_url'] = $item['down_url'] ?? '';
                    break;

                case 'news':
                    if (isset($item['content'])) {
                        $materialInfo['news_item'] = [];
                        foreach ($item['content']['news_item'] as $newsIndex => $newsItem) {
                            $materialInfo['news_item'][$newsIndex] = [
                                'title' => $newsItem['title'] ?? '',
                                'author' => $newsItem['author'] ?? '',
                                'digest' => $newsItem['digest'] ?? '',
                                'content' => mb_substr($newsItem['content'] ?? '', 0, 200) . '...', // 只记录前200字符
                                'content_source_url' => $newsItem['content_source_url'] ?? '',
                                'thumb_media_id' => $newsItem['thumb_media_id'] ?? '',
                                'thumb_url' => $newsItem['thumb_url'] ?? '',
                                'url' => $newsItem['url'] ?? ''
                            ];
                        }
                    }
                    break;
            }

            $logData['materials'][] = $materialInfo;
        }

        // 记录到专门的素材日志文件
        $this->logToFile('material_batch_get.log', $logData);

        // 同时记录到普通日志
        $this->wechatService->log('素材详细信息', $logData);
    }

    /**
     * 记录到指定日志文件
     *
     * @param string $filename
     * @param array $data
     */
    protected function logToFile(string $filename, array $data): void
    {
        $logFile = root_path() . 'runtime/log/' . $filename;
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $data
        ];

        $logContent = json_encode($logEntry, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
    }

    /**
     * HTTP GET请求
     *
     * @param string $url
     * @return string|false
     */
    protected function httpGet(string $url)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'WechatBot/1.0',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($error)) {
            $this->wechatService->log('HTTP GET请求失败', [
                'url' => $url,
                'error' => $error,
                'http_code' => $httpCode
            ]);
            return false;
        }

        if ($httpCode !== 200) {
            $this->wechatService->log('HTTP GET请求失败', [
                'url' => $url,
                'http_code' => $httpCode,
                'response' => $response
            ]);
            return false;
        }

        return $response;
    }

    /**
     * HTTP POST请求
     *
     * @param string $url
     * @param string $data
     * @return string|false
     */
    protected function httpPost(string $url, string $data)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'WechatBot/1.0',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data)
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($error)) {
            $this->wechatService->log('HTTP POST请求失败', [
                'url' => $url,
                'data' => $data,
                'error' => $error,
                'http_code' => $httpCode
            ]);
            return false;
        }

        if ($httpCode !== 200) {
            $this->wechatService->log('HTTP POST请求失败', [
                'url' => $url,
                'data' => $data,
                'http_code' => $httpCode,
                'response' => $response
            ]);
            return false;
        }

        return $response;
    }
}
