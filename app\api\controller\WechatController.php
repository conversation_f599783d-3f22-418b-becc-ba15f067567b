<?php
declare(strict_types=1);

namespace app\api\controller;

use app\BaseController;
use app\common\service\WechatService;
use think\Request;
use think\Response;

/**
 * 微信公众号控制器
 */
class WechatController extends BaseController
{
    /**
     * 微信服务实例
     * @var WechatService
     */
    protected $wechatService;

    /**
     * 微信公众号Token（从配置文件获取）
     * @var string
     */
    protected $token;

    /**
     * 微信公众号AppID
     * @var string
     */
    protected $appId;

    /**
     * 微信公众号AppSecret
     * @var string
     */
    protected $appSecret;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->wechatService = new WechatService();
        $this->token = config('wechat.token', '');
        $this->appId = config('wechat.app_id', '');
        $this->appSecret = config('wechat.app_secret', '');

        if (empty($this->token)) {
            throw new \Exception('微信Token未配置');
        }
    }

    /**
     * 微信服务器验证接口
     * GET请求用于验证服务器地址的有效性
     * POST请求用于接收微信服务器推送的消息
     * 
     * @param Request $request
     * @return Response
     */
    public function verify(Request $request): Response
    {
        try {
            // 获取请求参数
            $params = $request->param();
            // 记录请求日志
            $this->wechatService->log('微信服务器请求', [
                'method' => $request->method(),
                'params' => $params,
                'ip' => $request->ip()
            ]);
            
            $this->wechatService->log(json_encode($request->getInput()));

            // GET请求 - 验证服务器地址
            if ($request->isGet()) {
                return $this->handleVerification($params);
            }
            
            // POST请求 - 处理微信消息推送
            if ($request->isPost()) {
                return $this->handleMessage($request, $params);
            }

            return $this->error('不支持的请求方法');

        } catch (\Exception $e) {
            $this->wechatService->log('微信验证异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return $this->error('服务器内部错误');
        }
    }

    /**
     * 处理服务器验证
     * 
     * @param array $params
     * @return Response
     */
    protected function handleVerification(array $params): Response
    {
        $result = $this->wechatService->validateServer($params, $this->token);
        
        if (!$result['status']) {
            $this->wechatService->log('微信验证失败', [
                'message' => $result['message'],
                'params' => $params
            ]);
            
            return response('验证失败', 403);
        }

        $this->wechatService->log('微信验证成功', [
            'echostr' => $result['data']
        ]);

        // 返回echostr给微信服务器
        return response($result['data']);
    }

    /**
     * 处理微信消息推送
     * 
     * @param Request $request
     * @param array $params
     * @return Response
     */
    protected function handleMessage(Request $request, array $params): Response
    {
        // 验证签名
        $signature = $params['signature'] ?? '';
        $timestamp = $params['timestamp'] ?? '';
        $nonce = $params['nonce'] ?? '';

        if (!$this->wechatService->checkSignature($signature, $timestamp, $nonce, $this->token)) {
            $this->wechatService->log('消息推送签名验证失败', $params);
            return response('签名验证失败', 403);
        }

        // 获取POST数据（XML格式的消息内容）
        $postData = $request->getInput();
        $this->wechatService->log('接收到微信消息原始数据: ' . $postData);
        $this->wechatService->log('请求参数: ' . json_encode($params));

        // 解析XML消息
        $message = $this->parseWechatMessage($postData);

        if ($message) {
            $this->wechatService->log('消息解析成功: ' . json_encode($message));

            // 在处理消息时自动获取素材库信息
            $materialCount = (int)$request->param('material_count', 10);
            $this->autoFetchMaterials($materialCount);

            // 处理不同类型的消息
            $response = $this->processWechatMessage($message);
            $this->wechatService->log('消息处理完成，响应: ' . $response);

            return response($response)->contentType('application/xml');
        } else {
            $this->wechatService->log('消息解析失败，返回success');
            return response('success');
        }
    }

    /**
     * 解析微信消息XML
     *
     * @param string $xmlData
     * @return array|false
     */
    protected function parseWechatMessage(string $xmlData)
    {
        if (empty($xmlData)) {
            return false;
        }

        try {
            $xml = simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml === false) {
                $this->wechatService->log('XML解析失败');
                return false;
            }

            return [
                'ToUserName' => (string)$xml->ToUserName,
                'FromUserName' => (string)$xml->FromUserName,
                'CreateTime' => (string)$xml->CreateTime,
                'MsgType' => (string)$xml->MsgType,
                'MsgId' => (string)($xml->MsgId ?? ''),
                'Content' => (string)($xml->Content ?? ''),
                'MediaId' => (string)($xml->MediaId ?? ''),
                'PicUrl' => (string)($xml->PicUrl ?? ''),
                'Event' => (string)($xml->Event ?? ''),
                'EventKey' => (string)($xml->EventKey ?? ''),
            ];
        } catch (\Exception $e) {
            $this->wechatService->log('消息解析异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理微信消息
     *
     * @param array $message
     * @return string
     */
    protected function processWechatMessage(array $message): string
    {
        $msgType = $message['MsgType'];

        $this->wechatService->log('处理消息类型: ' . $msgType);

        switch ($msgType) {
            case 'text':
                return $this->handleTextMessage($message);
            case 'image':
                return $this->handleImageMessage($message);
            case 'event':
                return $this->handleEventMessage($message);
            default:
                return $this->replyTextMessage($message, '收到您的消息，感谢关注！');
        }
    }

    /**
     * 处理文本消息
     *
     * @param array $message
     * @return string
     */
    protected function handleTextMessage(array $message): string
    {
        $content = trim($message['Content']);
        $fromUser = $message['FromUserName'];

        $this->wechatService->log('处理文本消息: ' . $content . ', 来自用户: ' . $fromUser);

        // 检查是否是"收到不支持的消息类型，暂无法显示"
        if ($content === '[收到不支持的消息类型，暂无法显示]') {
            $this->wechatService->log('检测到不支持消息类型，返回小程序链接: ' . $fromUser);
            return $this->replyMiniProgramMessage($message, $fromUser);
        }

        // 普通文本消息的处理
        return $this->replyTextMessage($message, "收到您的消息: " . $content);
    }

    /**
     * 返回成功响应
     *
     * @param mixed $data
     * @param string $message
     * @return Response
     */
    protected function success($data = null, string $message = 'success'): Response
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 返回错误响应
     * 
     * @param string $message
     * @param int $code
     * @return Response
     */
    protected function error(string $message = 'error', int $code = 400): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => null
        ], $code);
    }

    /**
     * 获取微信Access Token
     *
     * @return string|false
     */
    protected function getAccessToken()
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            $this->wechatService->log('获取AccessToken失败: AppID或AppSecret未配置');
            return false;
        }

        // 检查缓存中是否有有效的access_token
        $cacheKey = 'wechat_access_token_' . md5($this->appId);
        $cachedToken = cache($cacheKey);

        if ($cachedToken) {
            $this->wechatService->log('使用缓存的AccessToken: ' . substr($cachedToken, 0, 20) . '...');
            return $cachedToken;
        }

        // 请求新的access_token
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";

        $this->wechatService->log('请求AccessToken: ' . $url);

        $response = $this->httpGet($url);
        if (!$response) {
            $this->wechatService->log('获取AccessToken失败: HTTP请求失败');
            return false;
        }

        $data = json_decode($response, true);
        if (!$data || isset($data['errcode'])) {
            $this->wechatService->log('获取AccessToken失败: ' . json_encode($data));
            return false;
        }

        $accessToken = $data['access_token'];
        $expiresIn = $data['expires_in'] ?? 7200;

        // 缓存access_token，提前5分钟过期
        cache($cacheKey, $accessToken, $expiresIn - 300);

        $this->wechatService->log('获取AccessToken成功: ' . substr($accessToken, 0, 20) . '..., 有效期: ' . $expiresIn . '秒');

        return $accessToken;
    }

    /**
     * 判断是否应该获取素材库
     *
     * @return bool
     */
    protected function shouldFetchMaterials(): bool
    {
        // 使用缓存控制获取频率，每10分钟最多获取一次
        $cacheKey = 'wechat_material_fetch_lock';
        $lastFetchTime = cache($cacheKey);

        if ($lastFetchTime && (time() - $lastFetchTime) < 600) { // 10分钟 = 600秒
            return false;
        }

        // 设置缓存锁
        cache($cacheKey, time(), 600);

        return true;
    }

    /**
     * 自动获取素材库信息（在处理消息时调用）
     *
     * @param int $count 获取数量，默认10个
     * @return void
     */
    protected function autoFetchMaterials(int $count = 10): void
    {
        try {
            // 检查是否启用自动获取功能
            if (!config('wechat.auto_fetch_materials', true)) {
                $this->wechatService->log('自动获取素材库功能已禁用');
                return;
            }

            // 检查是否需要获取素材库（避免频繁调用）
            if (!$this->shouldFetchMaterials()) {
                $this->wechatService->log('跳过素材库获取（基于频率控制）');
                return;
            }

            $this->wechatService->log('开始自动获取image类型素材库信息', ['count' => $count]);

            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                $this->wechatService->log('自动获取素材库失败: AccessToken获取失败');
                return;
            }

            // 只获取image类型素材
            $type = 'image';
            $this->wechatService->log("正在获取{$type}类型素材，数量: {$count}");

            $result = $this->callBatchGetMaterialAPI($accessToken, $type, 0, $count);

            if ($result['success']) {
                $materialData = $result['data'];
                $itemCount = $materialData['item_count'] ?? 0;
                $totalMaterialCount = $materialData['total_count'] ?? 0;

                $this->wechatService->log("获取{$type}素材成功", [
                    'type' => $type,
                    'total_count' => $totalMaterialCount,
                    'item_count' => $itemCount,
                    'requested_count' => $count
                ]);

                // 记录详细素材信息
                if ($itemCount > 0) {
                    $this->logMaterialDetails($materialData, $type);
                }

                // 将汇总信息记录到专用日志文件
                $this->logToFile('auto_material_fetch.log', [
                    'trigger' => 'message_received',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'type' => $type,
                    'requested_count' => $count,
                    'actual_count' => $itemCount,
                    'total_count' => $totalMaterialCount,
                    'materials' => $materialData
                ]);

                $this->wechatService->log('自动获取素材库完成', [
                    'type' => $type,
                    'requested_count' => $count,
                    'actual_count' => $itemCount,
                    'total_count' => $totalMaterialCount
                ]);
            } else {
                $this->wechatService->log("获取{$type}素材失败: " . $result['message']);
            }

        } catch (\Exception $e) {
            $this->wechatService->log('自动获取素材库异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * 批量获取素材库
     *
     * @param Request $request
     * @return Response
     */
    public function batchGetMaterial(Request $request): Response
    {
        try {
            $type = $request->param('type', 'image'); // image, voice, video, thumb, news
            $offset = (int)$request->param('offset', 0);
            $count = (int)$request->param('count', 20);

            // 验证参数
            $allowedTypes = ['image', 'voice', 'video', 'thumb', 'news'];
            if (!in_array($type, $allowedTypes)) {
                return $this->error('不支持的素材类型，支持的类型: ' . implode(', ', $allowedTypes));
            }

            if ($count > 20 || $count < 1) {
                return $this->error('count参数必须在1-20之间');
            }

            $this->wechatService->log('开始获取素材库', [
                'type' => $type,
                'offset' => $offset,
                'count' => $count
            ]);

            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return $this->error('获取AccessToken失败');
            }

            // 调用微信API获取素材
            $result = $this->callBatchGetMaterialAPI($accessToken, $type, $offset, $count);

            if ($result['success']) {
                // 记录成功日志
                $this->wechatService->log('素材库获取成功', [
                    'type' => $type,
                    'total_count' => $result['data']['total_count'] ?? 0,
                    'item_count' => $result['data']['item_count'] ?? 0,
                    'offset' => $offset,
                    'count' => $count
                ]);

                // 详细记录素材信息到日志
                $this->logMaterialDetails($result['data'], $type);

                return $this->success($result['data'], '素材库获取成功');
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            $this->wechatService->log('获取素材库异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return $this->error('服务器内部错误: ' . $e->getMessage());
        }
    }

    /**
     * 调用微信批量获取素材API
     *
     * @param string $accessToken
     * @param string $type
     * @param int $offset
     * @param int $count
     * @return array
     */
    protected function callBatchGetMaterialAPI(string $accessToken, string $type, int $offset, int $count): array
    {
        $url = "https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token={$accessToken}";

        $postData = json_encode([
            'type' => $type,
            'offset' => $offset,
            'count' => $count
        ]);

        $this->wechatService->log('调用素材API', [
            'url' => $url,
            'post_data' => $postData
        ]);

        $response = $this->httpPost($url, $postData);

        if (!$response) {
            return [
                'success' => false,
                'message' => 'HTTP请求失败'
            ];
        }

        $data = json_decode($response, true);

        // 记录原始响应
        $this->wechatService->log('素材API响应', [
            'response_length' => strlen($response),
            'response_data' => $data
        ]);

        if (!$data) {
            return [
                'success' => false,
                'message' => 'JSON解析失败'
            ];
        }

        if (isset($data['errcode']) && $data['errcode'] != 0) {
            return [
                'success' => false,
                'message' => '微信API错误: ' . ($data['errmsg'] ?? '未知错误') . ' (错误码: ' . $data['errcode'] . ')'
            ];
        }

        return [
            'success' => true,
            'data' => $data
        ];
    }

    /**
     * 记录素材详细信息到日志
     *
     * @param array $materialData
     * @param string $type
     */
    protected function logMaterialDetails(array $materialData, string $type): void
    {
        if (!isset($materialData['item'])) {
            return;
        }

        $logData = [
            'type' => $type,
            'total_count' => $materialData['total_count'] ?? 0,
            'item_count' => $materialData['item_count'] ?? 0,
            'materials' => []
        ];

        foreach ($materialData['item'] as $index => $item) {
            $materialInfo = [
                'index' => $index,
                'media_id' => $item['media_id'] ?? '',
                'update_time' => $item['update_time'] ?? 0,
                'update_time_formatted' => isset($item['update_time']) ? date('Y-m-d H:i:s', $item['update_time']) : ''
            ];

            // 根据素材类型添加特定信息
            switch ($type) {
                case 'image':
                case 'thumb':
                    $materialInfo['name'] = $item['name'] ?? '';
                    $materialInfo['url'] = $item['url'] ?? '';
                    break;

                case 'voice':
                    $materialInfo['name'] = $item['name'] ?? '';
                    $materialInfo['url'] = $item['url'] ?? '';
                    break;

                case 'video':
                    $materialInfo['name'] = $item['name'] ?? '';
                    $materialInfo['introduction'] = $item['introduction'] ?? '';
                    $materialInfo['down_url'] = $item['down_url'] ?? '';
                    break;

                case 'news':
                    if (isset($item['content'])) {
                        $materialInfo['news_item'] = [];
                        foreach ($item['content']['news_item'] as $newsIndex => $newsItem) {
                            $materialInfo['news_item'][$newsIndex] = [
                                'title' => $newsItem['title'] ?? '',
                                'author' => $newsItem['author'] ?? '',
                                'digest' => $newsItem['digest'] ?? '',
                                'content' => mb_substr($newsItem['content'] ?? '', 0, 200) . '...', // 只记录前200字符
                                'content_source_url' => $newsItem['content_source_url'] ?? '',
                                'thumb_media_id' => $newsItem['thumb_media_id'] ?? '',
                                'thumb_url' => $newsItem['thumb_url'] ?? '',
                                'url' => $newsItem['url'] ?? ''
                            ];
                        }
                    }
                    break;
            }

            $logData['materials'][] = $materialInfo;
        }

        // 记录到专门的素材日志文件
        $this->logToFile('material_batch_get.log', $logData);

        // 同时记录到普通日志
        $this->wechatService->log('素材详细信息', $logData);
    }

    /**
     * 记录到指定日志文件
     *
     * @param string $filename
     * @param array $data
     */
    protected function logToFile(string $filename, array $data): void
    {
        $logFile = root_path() . 'runtime/log/' . $filename;
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $data
        ];

        $logContent = json_encode($logEntry, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
    }

    /**
     * HTTP GET请求
     *
     * @param string $url
     * @return string|false
     */
    protected function httpGet(string $url)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'WechatBot/1.0',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($error)) {
            $this->wechatService->log('HTTP GET请求失败', [
                'url' => $url,
                'error' => $error,
                'http_code' => $httpCode
            ]);
            return false;
        }

        if ($httpCode !== 200) {
            $this->wechatService->log('HTTP GET请求失败', [
                'url' => $url,
                'http_code' => $httpCode,
                'response' => $response
            ]);
            return false;
        }

        return $response;
    }

    /**
     * HTTP POST请求
     *
     * @param string $url
     * @param string $data
     * @return string|false
     */
    protected function httpPost(string $url, string $data)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'WechatBot/1.0',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data)
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($error)) {
            $this->wechatService->log('HTTP POST请求失败', [
                'url' => $url,
                'data' => $data,
                'error' => $error,
                'http_code' => $httpCode
            ]);
            return false;
        }

        if ($httpCode !== 200) {
            $this->wechatService->log('HTTP POST请求失败', [
                'url' => $url,
                'data' => $data,
                'http_code' => $httpCode,
                'response' => $response
            ]);
            return false;
        }

        return $response;
    }

    /**
     * 处理图片消息
     *
     * @param array $message
     * @return string
     */
    protected function handleImageMessage(array $message): string
    {
        $picUrl = $message['PicUrl'];
        $fromUser = $message['FromUserName'];

        $this->wechatService->log('收到图片消息: ' . $picUrl . ', 来自用户: ' . $fromUser);

        return $this->replyTextMessage($message, "收到您的图片，正在处理中...");
    }

    /**
     * 处理事件消息
     *
     * @param array $message
     * @return string
     */
    protected function handleEventMessage(array $message): string
    {
        $event = $message['Event'];
        $fromUser = $message['FromUserName'];

        $this->wechatService->log('处理事件消息: ' . $event . ', 来自用户: ' . $fromUser);

        switch ($event) {
            case 'subscribe':
                return $this->replyTextMessage($message, '欢迎关注！发送"收到不支持的消息类型，暂无法显示"可以查看您的专属表情包哦~ 😊');
            case 'unsubscribe':
                $this->wechatService->log('用户取消关注: ' . $fromUser);
                return 'success';
            default:
                return 'success';
        }
    }

    /**
     * 回复文本消息
     *
     * @param array $message
     * @param string $content
     * @return string
     */
    protected function replyTextMessage(array $message, string $content): string
    {
        $template = "<xml>
<ToUserName><![CDATA[%s]]></ToUserName>
<FromUserName><![CDATA[%s]]></FromUserName>
<CreateTime>%s</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[%s]]></Content>
</xml>";

        return sprintf(
            $template,
            $message['FromUserName'],
            $message['ToUserName'],
            time(),
            $content
        );
    }

    /**
     * 回复小程序消息
     *
     * @param array $message
     * @param string $fromUser
     * @return string
     */
    protected function replyMiniProgramMessage(array $message, string $fromUser): string
    {
        // 小程序配置
        $appId = 'wxa484f0497f834791';
        $pagePath = 'pages/gif/gif';
        $openid = $fromUser; // 使用发送消息的用户openid

        // 构建小程序页面路径（带参数）
        $pagePathWithParams = $pagePath . '?openid=' . $openid;

        // 记录小程序回复日志
        $this->wechatService->log('回复小程序消息: appId=' . $appId . ', pagePath=' . $pagePathWithParams . ', fromUser=' . $fromUser);

        $template = "<xml>
<ToUserName><![CDATA[%s]]></ToUserName>
<FromUserName><![CDATA[%s]]></FromUserName>
<CreateTime>%s</CreateTime>
<MsgType><![CDATA[miniprogrampage]]></MsgType>
<Title><![CDATA[%s]]></Title>
<AppId><![CDATA[%s]]></AppId>
<PagePath><![CDATA[%s]]></PagePath>
<ThumbMediaId><![CDATA[%s]]></ThumbMediaId>
</xml>";

        return sprintf(
            $template,
            $message['FromUserName'],
            $message['ToUserName'],
            time(),
            '点击查看您的专属表情包', // 小程序卡片标题
            $appId,
            $pagePathWithParams,
            '' // ThumbMediaId 可以为空，或者设置一个默认的缩略图媒体ID
        );
    }
}
