<?php
declare(strict_types=1);

namespace app\common\service;

/**
 * 微信公众号服务类
 */
class WechatService
{
    /**
     * 验证微信服务器token
     * 
     * @param string $signature 微信加密签名
     * @param string $timestamp 时间戳
     * @param string $nonce 随机数
     * @param string $token 开发者token
     * @return bool
     */
    public function checkSignature(string $signature, string $timestamp, string $nonce, string $token): bool
    {
        // 将token、timestamp、nonce三个参数进行字典序排序
        $tmpArr = [$token, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);
        
        // 将三个参数字符串拼接成一个字符串进行sha1加密
        $tmpStr = implode($tmpArr);
        $tmpStr = sha1($tmpStr);
        
        // 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
        return $tmpStr === $signature;
    }

    /**
     * 响应微信服务器验证请求
     * 
     * @param string $echostr 随机字符串
     * @return string
     */
    public function responseEchoStr(string $echostr): string
    {
        return $echostr;
    }

    /**
     * 验证微信服务器请求的完整流程
     * 
     * @param array $params 请求参数
     * @param string $token 开发者token
     * @return array
     */
    public function validateServer(array $params, string $token): array
    {
        $signature = $params['signature'] ?? '';
        $timestamp = $params['timestamp'] ?? '';
        $nonce = $params['nonce'] ?? '';
        $echostr = $params['echostr'] ?? '';

        // 验证必要参数
        if (empty($signature) || empty($timestamp) || empty($nonce)) {
            return [
                'status' => false,
                'message' => '缺少必要参数',
                'data' => null
            ];
        }

        // 验证签名
        if (!$this->checkSignature($signature, $timestamp, $nonce, $token)) {
            return [
                'status' => false,
                'message' => '签名验证失败',
                'data' => null
            ];
        }

        return [
            'status' => true,
            'message' => '验证成功',
            'data' => $echostr
        ];
    }

    /**
     * 记录日志
     * 
     * @param string $message 日志信息
     * @param array $context 上下文数据
     * @return void
     */
    public function log(string $message, array $context = []): void
    {
        // 可以使用ThinkPHP的日志功能
        \think\facade\Log::info($message, $context);
    }
}
