<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动获取素材库测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007cba;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .btn {
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .config-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .log-viewer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 10px;
            padding: 5px;
            border-left: 3px solid #007cba;
            background: white;
        }
        .log-timestamp {
            color: #666;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 自动获取素材库测试</h1>
        
        <div class="config-info">
            <h3>📋 功能说明</h3>
            <p>当微信用户发送消息时，系统会自动获取图片素材库信息并记录到日志中。</p>
            <ul>
                <li><strong>触发条件</strong>：接收到微信消息时自动触发</li>
                <li><strong>频率控制</strong>：每10分钟最多获取一次，避免频繁调用</li>
                <li><strong>素材类型</strong>：仅获取图片(image)类型素材</li>
                <li><strong>获取数量</strong>：可通过URL参数 <code>material_count</code> 控制，默认10个</li>
                <li><strong>日志记录</strong>：详细信息保存到 <code>auto_material_fetch.log</code></li>
            </ul>
        </div>
        
        <div class="section">
            <h3>🧪 模拟微信消息</h3>
            <p>发送模拟消息来触发自动获取素材库功能：</p>
            <div style="margin-bottom: 15px;">
                <label for="material-count">获取数量:</label>
                <input type="number" id="material-count" value="10" min="1" max="20" style="width: 80px; padding: 5px; margin: 0 10px;">
                <small>（1-20个图片素材）</small>
            </div>
            <button class="btn" onclick="sendTestMessage()">发送测试消息（默认数量）</button>
            <button class="btn" onclick="sendTestMessageWithCount()">发送消息（指定数量）</button>
            <button class="btn" onclick="sendImageMessage()">发送图片消息</button>
            <button class="btn" onclick="sendTextMessage()">发送文本消息</button>
            <div id="message-result"></div>
        </div>
        
        <div class="section">
            <h3>📊 查看日志</h3>
            <p>查看自动获取素材库的日志记录：</p>
            <button class="btn" onclick="viewAutoFetchLogs()">查看自动获取日志</button>
            <button class="btn" onclick="viewSystemLogs()">查看系统日志</button>
            <button class="btn danger" onclick="clearLogs()">清空日志显示</button>
            <div id="logs-container"></div>
        </div>
        
        <div class="section">
            <h3>⚙️ 配置管理</h3>
            <p>当前配置信息：</p>
            <ul>
                <li><strong>自动获取开关</strong>：通过 <code>WECHAT_AUTO_FETCH_MATERIALS</code> 环境变量控制</li>
                <li><strong>获取数量控制</strong>：通过URL参数 <code>material_count</code> 指定（1-20），默认10个</li>
                <li><strong>素材类型</strong>：固定为图片(image)类型</li>
                <li><strong>频率控制</strong>：每10分钟最多获取一次</li>
            </ul>
            <p><strong>示例URL</strong>：<code>/api/wechat/verify?material_count=5</code></p>
            <button class="btn" onclick="checkConfig()">检查当前配置</button>
            <div id="config-result"></div>
        </div>
        
        <div class="section">
            <h3>🔍 调试工具</h3>
            <p>其他调试和管理工具：</p>
            <a href="/api/wechat/debug" class="btn" target="_blank">微信调试页面</a>
            <a href="/test_material.html" class="btn" target="_blank">素材库测试</a>
            <a href="/images/manage" class="btn" target="_blank">图片管理</a>
        </div>
    </div>
    
    <script>
        async function sendTestMessage() {
            await sendMessageWithParams('');
        }

        async function sendTestMessageWithCount() {
            const count = document.getElementById('material-count').value;
            await sendMessageWithParams(`?material_count=${count}`);
        }

        async function sendMessageWithParams(params) {
            const resultDiv = document.getElementById('message-result');
            resultDiv.innerHTML = '<div class="result">正在发送测试消息...</div>';

            const testXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[test_user_${Date.now()}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[测试自动获取素材库功能]]></Content>
<MsgId>${Date.now()}</MsgId>
</xml>`;

            try {
                const response = await fetch(`/api/wechat/verify${params}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: testXml
                });

                const responseText = await response.text();
                const count = params ? params.split('=')[1] : '默认(10)';

                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>✅ 消息发送成功</h4>
                        <p><strong>响应:</strong> ${responseText}</p>
                        <p><strong>获取数量:</strong> ${count} 个图片素材</p>
                        <p><strong>提示:</strong> 如果启用了自动获取功能，系统会在后台获取指定数量的图片素材</p>
                        <p><strong>建议:</strong> 等待几秒后查看日志，看是否有素材库获取记录</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 发送失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function sendImageMessage() {
            const resultDiv = document.getElementById('message-result');
            resultDiv.innerHTML = '<div class="result">正在发送图片消息...</div>';
            
            const imageXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[test_user_${Date.now()}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[image]]></MsgType>
<PicUrl><![CDATA[https://via.placeholder.com/300x200.jpg?text=Test+Auto+Material]]></PicUrl>
<MediaId><![CDATA[test_media_${Date.now()}]]></MediaId>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: imageXml
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>✅ 图片消息发送成功</h4>
                        <p><strong>响应:</strong> ${responseText}</p>
                        <p><strong>说明:</strong> 图片消息会触发自动素材库获取功能</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 发送失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function sendTextMessage() {
            const resultDiv = document.getElementById('message-result');
            resultDiv.innerHTML = '<div class="result">正在发送文本消息...</div>';
            
            const textXml = `<xml>
<ToUserName><![CDATA[gh_test]]></ToUserName>
<FromUserName><![CDATA[test_user_${Date.now()}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[你好 😊 这是一个测试消息，会触发素材库获取]]></Content>
<MsgId>${Date.now()}</MsgId>
</xml>`;
            
            try {
                const response = await fetch('/api/wechat/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/xml',
                    },
                    body: textXml
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h4>✅ 文本消息发送成功</h4>
                        <p><strong>响应:</strong> ${responseText}</p>
                        <p><strong>说明:</strong> 包含表情的文本消息会触发自动素材库获取</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ 发送失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function viewAutoFetchLogs() {
            const logsContainer = document.getElementById('logs-container');
            logsContainer.innerHTML = '<div class="result">正在获取自动获取日志...</div>';
            
            try {
                const response = await fetch('/api/wechat/debug/logs?type=all&lines=50');
                const data = await response.json();
                
                if (data.code === 200) {
                    let html = '<div class="result success"><h4>📋 自动获取素材库日志</h4></div>';
                    html += '<div class="log-viewer">';
                    
                    // 显示ThinkPHP日志中与素材相关的条目
                    if (data.data.logs.thinkphp) {
                        const materialLogs = data.data.logs.thinkphp.filter(log => 
                            log.includes('素材') || log.includes('material') || log.includes('自动获取')
                        );
                        
                        if (materialLogs.length > 0) {
                            materialLogs.forEach(log => {
                                html += `<div class="log-entry">${log}</div>`;
                            });
                        } else {
                            html += '<div class="log-entry">暂无素材相关日志记录</div>';
                        }
                    }
                    
                    html += '</div>';
                    logsContainer.innerHTML = html;
                } else {
                    logsContainer.innerHTML = `
                        <div class="result error">
                            <h4>❌ 获取日志失败</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                logsContainer.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function viewSystemLogs() {
            const logsContainer = document.getElementById('logs-container');
            logsContainer.innerHTML = '<div class="result">正在获取系统日志...</div>';
            
            try {
                const response = await fetch('/api/wechat/debug/logs?type=all&lines=30');
                const data = await response.json();
                
                if (data.code === 200) {
                    let html = '<div class="result success"><h4>📋 系统日志</h4></div>';
                    html += '<div class="log-viewer">';
                    
                    if (data.data.logs.thinkphp && data.data.logs.thinkphp.length > 0) {
                        data.data.logs.thinkphp.slice(0, 20).forEach(log => {
                            html += `<div class="log-entry">${log}</div>`;
                        });
                    } else {
                        html += '<div class="log-entry">暂无系统日志</div>';
                    }
                    
                    html += '</div>';
                    logsContainer.innerHTML = html;
                } else {
                    logsContainer.innerHTML = `
                        <div class="result error">
                            <h4>❌ 获取日志失败</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                logsContainer.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function clearLogs() {
            document.getElementById('logs-container').innerHTML = '';
        }
        
        async function checkConfig() {
            const configResult = document.getElementById('config-result');
            configResult.innerHTML = '<div class="result">正在检查配置...</div>';
            
            try {
                const response = await fetch('/api/wechat/debug/status');
                const data = await response.json();
                
                if (data.code === 200) {
                    configResult.innerHTML = `
                        <div class="result success">
                            <h4>⚙️ 当前配置状态</h4>
                            <pre>${JSON.stringify(data.data.config, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    configResult.innerHTML = `
                        <div class="result error">
                            <h4>❌ 获取配置失败</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                configResult.innerHTML = `
                    <div class="result error">
                        <h4>❌ 请求失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
